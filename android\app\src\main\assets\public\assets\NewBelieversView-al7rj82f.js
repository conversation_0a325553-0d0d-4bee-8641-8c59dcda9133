import{j as e}from"./utils-DJ-5fhzp.js";import{r as s}from"./charts-DCWOOedL.js";import{f as t,u as a,M as r,B as i,U as n,E as l,T as c,P as d,C as o,j as m,k as x,l as h,A as u,H as g,m as j,a as N,I as p,n as v,o as f,p as y,N as w}from"./index-DXKcng7s.js";import{b,c as B,T as C,d as k}from"./icons-CQztrb4S.js";import{B as S,T as D}from"./Badge-BonlZcKp.js";import{A as T}from"./AttendanceMarker-TEPqVtRg.js";import"./vendor-CMmhtoO5.js";class F{static parseText(e){const s=e.split("\n").map((e=>e.trim())).filter((e=>e.length>0)),a=[],r=[];let i=t(new Date);for(let t=0;t<s.length;t++){const e=s[t];try{const s=this.extractDateFromHeader(e);if(s){i=s;continue}if(e.startsWith("#"))continue;const t=this.parseBelieverLine(e,i);t&&a.push(t)}catch(n){r.push(`Line ${t+1}: ${n instanceof Error?n.message:"Unknown error"}`)}}return{newBelievers:a,totalLines:s.length,successfullyParsed:a.length,errors:r}}static extractDateFromHeader(e){const s=e.match(/(\d{1,2}-[A-Za-z]{3}-\d{2,4})/);if(s)try{const e=s[1],[t,a,r]=e.split("-"),i={Jan:"01",Feb:"02",Mar:"03",Apr:"04",May:"05",Jun:"06",Jul:"07",Aug:"08",Sep:"09",Oct:"10",Nov:"11",Dec:"12"}[a],n=2===r.length?`20${r}`:r;if(i)return`${n}-${i}-${t.padStart(2,"0")}`}catch(t){}return null}static parseBelieverLine(e,s){if(!e||e.length<10)return null;const t=e.replace(/^\d+\s*/,"").trim();if(!t)return null;const a=this.intelligentSplit(t);if(a.length<3)return null;const r={name:"",surname:"",contact:"",dateOfBirth:"",residence:"",studies:"",campus:"",occupation:"",year:"",isFirstTime:!1,ministry:"",joinedDate:s,rawText:e,confidence:0,issues:[]};let i=0;a[i]&&(r.name=a[i++]),a[i]&&(r.surname=a[i++]),a[i]&&(r.contact=this.normalizeContact(a[i++])),a[i]&&(r.dateOfBirth=this.normalizeDateOfBirth(a[i++])),a[i]&&(r.residence=a[i++]),a[i]&&(r.studies=a[i++]),a[i]&&(r.campus=a[i++]),a[i]&&(r.occupation=a[i++]),a[i]&&(r.year=a[i++]);const n=a.slice(i).join(" ");return r.isFirstTime=this.parseFirstTime(n),r.ministry=this.extractMinistry(n),r.confidence=this.calculateConfidence(r),this.addIssues(r),r.name?r:null}static intelligentSplit(e){const s=[],t=e.split(/\s+/);let a="",r=0;for(let i=0;i<t.length;i++){const e=t[i];0!==r?1!==r?2===r&&this.isContact(e)||3===r&&this.isDateOfBirth(e)?(s.push(e),r++):(a?a+=" "+e:a=e,this.shouldEndField(a,r,t,i)&&(s.push(a),a="",r++)):(s.push(e),r++):(s.push(e),r++)}return a&&s.push(a),s}static isContact(e){return/^[0-9+\-\s()]+$/.test(e)&&e.length>=7}static isDateOfBirth(e){return/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(e)}static shouldEndField(e,s,t,a){const r=t[a+1];switch(s){case 4:return e.split(" ").length>=2||/BSc|BA|BCom|BEng|Accounting|Science|Arts|Engineering/i.test(r||"");case 5:return/Campus|Wits|UCT|UJ|University/i.test(r||"");case 6:return/Student|Engineer|Doctor|Teacher|Main/i.test(r||"");case 7:return/1st|2nd|3rd|4th|Year|Final|Student/i.test(r||"");case 8:return/No|Yes|First|Rededication/i.test(r||"");default:return!1}}static normalizeContact(e){let s=e.replace(/[^\d+]/g,"");return/^0\d{9}$/.test(s)?"+27"+s.substring(1):/^\d{9}$/.test(s)?"+27"+s:e}static normalizeDateOfBirth(e){const s=e.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);if(s){const[,e,t,a]=s;return`${a}-${t.padStart(2,"0")}-${e.padStart(2,"0")}`}return e}static parseFirstTime(e){const s=e.toLowerCase();return!!(s.includes("1st")||s.includes("first")||s.includes("yes"))||(s.includes("no")||s.includes("rededication"),!1)}static extractMinistry(e){const s=["Choir","Dancing Stars","Ushers","Arrival Stars","Airport Stars","Media"];for(const t of s)if(e.toLowerCase().includes(t.toLowerCase()))return t;return""}static calculateConfidence(e){let s=0,t=0;return t+=30,e.name&&e.name.length>1&&(s+=30),t+=20,e.surname&&e.surname.length>1&&(s+=20),t+=20,e.contact&&(e.contact.includes("+")||e.contact.length>=10)&&(s+=20),t+=10,e.dateOfBirth&&e.dateOfBirth.includes("-")&&(s+=10),t+=20,e.residence&&(s+=3),e.studies&&(s+=3),e.campus&&(s+=3),e.occupation&&(s+=3),e.year&&(s+=3),e.ministry&&(s+=5),Math.min(s/100,1)}static addIssues(e){e.name||e.issues.push("No name detected"),e.surname||e.issues.push("No surname detected"),e.contact?!e.contact.includes("+")&&e.contact.length<10&&e.issues.push("Contact might be invalid"):e.issues.push("No contact information found"),e.dateOfBirth||e.issues.push("No date of birth provided"),e.name&&e.name.length<2&&e.issues.push("Name seems too short"),e.ministry||e.issues.push("No ministry specified")}static convertToNewBeliever(e){return{name:e.name,surname:e.surname||"",contact:e.contact||"",dateOfBirth:e.dateOfBirth||"",residence:e.residence||"",studies:e.studies||"",campus:e.campus||"",occupation:e.occupation||"",year:e.year||"",isFirstTime:e.isFirstTime,ministry:e.ministry||"",joinedDate:e.joinedDate}}}const M=({isOpen:t,onClose:n})=>{const{addMultipleNewBelieversHandler:l}=a(),[c,d]=s.useState(""),[o,m]=s.useState(null),[x,h]=s.useState("input"),[u,g]=s.useState(0),[j,N]=s.useState([]);s.useEffect((()=>{t&&(d(""),m(null),h("input"),g(0),N([]))}),[t]);const p=e=>e>=.8?"text-green-600":e>=.6?"text-yellow-600":"text-red-600",v=s=>s>=.8?e.jsx(B,{className:"w-4 h-4 text-green-600"}):s>=.6?e.jsx(C,{className:"w-4 h-4 text-yellow-600"}):e.jsx(k,{className:"w-4 h-4 text-red-600"});return e.jsx(r,{isOpen:t,onClose:n,title:"Add Multiple New Believers",size:"xl",children:e.jsxs("div",{className:"space-y-6",children:["input"===x&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-green-600 text-sm",children:"🌱"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-green-800 mb-1",children:"Smart Paste for New Believers"}),e.jsx("p",{className:"text-sm text-green-700",children:"Paste new believer information and we'll automatically detect names and contact information. Each line should contain one new believer's information."}),e.jsxs("p",{className:"text-xs text-green-600 mt-2",children:["Examples:",e.jsx("br",{}),'• "1. John Smith - +27 81 872 6246"',e.jsx("br",{}),'• "2. Mary Jane - <EMAIL>"',e.jsx("br",{}),'• "3. Peter Parker - +27 60 122 7828"',e.jsx("br",{}),'• "4. Sarah Wilson"']})]})]})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"New Believer Information"}),e.jsx(i,{type:"button",variant:"secondary",size:"sm",onClick:async()=>{try{const e=await navigator.clipboard.readText();if(d(e),e.trim().length>0){const s=F.parseText(e);m(s)}}catch(e){}},leftIcon:e.jsx(b,{className:"w-4 h-4"}),children:"Paste from Clipboard"})]}),e.jsx("textarea",{value:c,onChange:e=>{const s=e.target.value;if(d(s),s.trim().length>0){const e=F.parseText(s);m(e)}else m(null)},placeholder:"Paste new believer information here, one person per line...",className:"w-full h-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"})]})}),o&&e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:["Detected: ",o.successfullyParsed," of ",o.totalLines," entries"]}),o.successfullyParsed>0&&e.jsx(i,{onClick:()=>{o&&o.newBelievers.length>0&&h("preview")},variant:"primary",size:"sm",children:"Review & Add"})]}),o.errors.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("h5",{className:"text-sm font-medium text-red-600 mb-1",children:"Parsing Issues:"}),e.jsx("ul",{className:"text-xs text-red-500 space-y-1",children:o.errors.map(((s,t)=>e.jsxs("li",{children:["• ",s]},t)))})]}),e.jsxs("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:[o.newBelievers.slice(0,5).map(((s,t)=>e.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[v(s.confidence),e.jsxs("span",{className:"font-medium",children:[s.name," ",s.surname]}),s.contact&&e.jsxs("span",{className:"text-gray-500",children:["• ",s.contact]})]}),e.jsxs("span",{className:`text-xs ${p(s.confidence)}`,children:[Math.round(100*s.confidence),"% confident"]})]},t))),o.newBelievers.length>5&&e.jsxs("div",{className:"text-center text-sm text-gray-500 py-2",children:["... and ",o.newBelievers.length-5," more"]})]})]})]}),"preview"===x&&o&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4",children:[e.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"Review New Believers"}),e.jsx("p",{className:"text-sm text-blue-700",children:"Review the detected information below. You can go back to make changes or proceed to add all new believers."})]}),e.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:o.newBelievers.map(((s,t)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[v(s.confidence),e.jsxs("span",{className:"font-medium text-gray-900",children:[s.name," ",s.surname]}),e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${p(s.confidence)}`,children:[Math.round(100*s.confidence),"% confident"]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Contact:"}),e.jsx("span",{className:"ml-2 text-gray-900",children:s.contact||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Original:"}),e.jsx("span",{className:"ml-2 text-gray-500 italic",children:s.rawText})]})]}),s.issues.length>0&&e.jsxs("div",{className:"mt-2 p-2 bg-yellow-50 rounded border border-yellow-200",children:[e.jsx("h6",{className:"text-xs font-medium text-yellow-800",children:"Potential Issues:"}),e.jsx("ul",{className:"text-xs text-yellow-700 mt-1",children:s.issues.map(((s,t)=>e.jsxs("li",{children:["• ",s]},t)))})]})]},t)))}),e.jsxs("div",{className:"flex justify-between pt-4",children:[e.jsx(i,{onClick:()=>h("input"),variant:"secondary",children:"Back to Edit"}),e.jsxs(i,{onClick:async()=>{if(o){h("processing");try{const e=o.newBelievers.map((e=>F.convertToNewBeliever(e))),s=await l(e);g(s.successful.length),N(s.failed.map((e=>`Failed to add ${e.data.name} ${e.data.surname}: ${e.error}`)))}catch(e){g(0),N([`Bulk operation failed: ${e instanceof Error?e.message:"Unknown error"}`])}finally{h("complete")}}},variant:"primary",children:["Add ",o.newBelievers.length," New Believers"]})]})]}),"processing"===x&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"}),e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Adding New Believers..."}),e.jsx("p",{className:"text-sm text-gray-600",children:"Please wait while we process your entries."})]}),"complete"===x&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center py-6",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(B,{className:"w-8 h-8 text-green-600"})}),e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Bulk Add Complete!"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[u>0&&`Successfully added ${u} new believers.`,j.length>0&&` ${j.length} entries failed.`]})]}),j.length>0&&e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-red-800 mb-2",children:"Failed Entries:"}),e.jsx("ul",{className:"text-sm text-red-700 space-y-1",children:j.map(((s,t)=>e.jsxs("li",{children:["• ",s]},t)))})]}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsx(i,{onClick:n,variant:"primary",children:"Close"})})]})]})})},A=({isOpen:t,onClose:p,newBeliever:v})=>{const{attendanceRecords:f,displayedSundays:y,markNewBelieverAttendanceHandler:w,openNewBelieverForm:b,deleteNewBelieverHandler:B,showConfirmation:C}=a();if(!v)return null;const k=e=>{const s=f.find((s=>s.newBelieverId===v.id&&s.date===e));return s?.status},D=s.useMemo((()=>{if(0===y.length)return{present:0,absent:0,late:0,rate:0};const e=y.filter((e=>"Present"===k(e))).length;return{present:e,absent:y.filter((e=>"Absent"===k(e))).length,late:y.filter((e=>"Late"===k(e))).length,rate:Math.round(e/y.length*100)}}),[y,f,v.id]),F=()=>{b(v),p()},M=Math.floor(((new Date).getTime()-new Date(v.joinedDate).getTime())/864e5);return e.jsx(r,{isOpen:t,onClose:p,title:"New Believer Details",size:"xl",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center",children:e.jsx(n,{className:"w-8 h-8 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:`${v.name}${v.surname?` ${v.surname}`:""}`}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[v.isFirstTime&&e.jsx(S,{variant:"success",children:"First Time Visitor"}),v.ministry&&e.jsx(S,{variant:"info",children:v.ministry})]})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(i,{variant:"secondary",leftIcon:e.jsx(l,{className:"w-4 h-4"}),onClick:F,children:"Edit"}),e.jsx(i,{variant:"danger",leftIcon:e.jsx(c,{className:"w-4 h-4"}),onClick:()=>{C("deleteNewBeliever",v,(()=>{B(v.id),p()}))},children:"Delete"})]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-xl p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(n,{className:"w-5 h-5 mr-2 text-blue-600"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[v.contact&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(d,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Contact"}),e.jsx("div",{className:"font-medium",children:v.contact})]})]}),v.dateOfBirth&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(o,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Date of Birth"}),e.jsx("div",{className:"font-medium",children:m(v.dateOfBirth)})]})]}),v.residence&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(x,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Residence"}),e.jsx("div",{className:"font-medium",children:v.residence})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(h,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Joined Date"}),e.jsxs("div",{className:"font-medium",children:[m(v.joinedDate)," (",M," days ago)"]})]})]})]})]}),(v.studies||v.campus||v.occupation||v.year)&&e.jsxs("div",{className:"bg-blue-50 rounded-xl p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(u,{className:"w-5 h-5 mr-2 text-blue-600"}),"Education & Work"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[v.studies&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(u,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Studies"}),e.jsx("div",{className:"font-medium",children:v.studies})]})]}),v.campus&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(g,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Campus"}),e.jsx("div",{className:"font-medium",children:v.campus})]})]}),v.occupation&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(j,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Occupation"}),e.jsx("div",{className:"font-medium",children:v.occupation})]})]}),v.year&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(o,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Year"}),e.jsx("div",{className:"font-medium",children:v.year})]})]})]})]}),e.jsxs("div",{className:"bg-purple-50 rounded-xl p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(o,{className:"w-5 h-5 mr-2 text-purple-600"}),"Attendance Summary"]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:D.present}),e.jsx("div",{className:"text-sm text-gray-500",children:"Present"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:D.absent}),e.jsx("div",{className:"text-sm text-gray-500",children:"Absent"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:D.late}),e.jsx("div",{className:"text-sm text-gray-500",children:"Late"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[D.rate,"%"]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Rate"})]})]}),y.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"This Month's Attendance"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3",children:y.map((s=>{const t=k(s);return e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xs text-gray-500 mb-1",children:N(s)}),e.jsx(T,{memberId:v.id,date:s,currentStatus:t,onMarkAttendance:(e,s,t)=>w(e,s,t)})]},s)}))})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[e.jsx(i,{variant:"secondary",onClick:p,children:"Close"}),e.jsx(i,{variant:"primary",onClick:F,leftIcon:e.jsx(l,{className:"w-4 h-4"}),children:"Edit Details"})]})]})})},$=()=>{const{newBelievers:t,attendanceRecords:r,openNewBelieverForm:h,deleteNewBelieverHandler:u,markNewBelieverAttendanceHandler:g,isLoading:j,displayedSundays:v}=a(),[f,y]=s.useState(""),[w,b]=s.useState(""),[B,C]=s.useState(!1),[k,F]=s.useState(null),M=s.useMemo((()=>{const e=t.map((e=>e.ministry)).filter((e=>e&&""!==e.trim()));return[...new Set(e)].sort()}),[t]),$=s.useMemo((()=>t.filter((e=>{const s=""===f||e.name.toLowerCase().includes(f.toLowerCase())||e.surname.toLowerCase().includes(f.toLowerCase())||e.contact.toLowerCase().includes(f.toLowerCase()),t=""===w||e.ministry===w,a=!B||e.isFirstTime;return s&&t&&a})).sort(((e,s)=>new Date(s.joinedDate).getTime()-new Date(e.joinedDate).getTime()))),[t,f,w,B]),O=(e,s)=>{const t=r.find((t=>t.newBelieverId===e&&t.date===s));return t?.status},L=e=>{if(0===v.length)return 0;const s=v.filter((s=>"Present"===O(e,s))).length;return Math.round(s/v.length*100)},P=e=>`${e.name}${e.surname?` ${e.surname}`:""}`,E=[{key:"name",header:"New Believer",width:"25%",render:s=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center",children:e.jsx(n,{className:"w-5 h-5 text-green-600"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold text-gray-900 text-lg",children:P(s)}),e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[s.isFirstTime&&e.jsx(S,{variant:"success",size:"sm",children:"First Time"}),s.ministry&&e.jsx("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full",children:s.ministry})]})]})]})},{key:"contact",header:"Contact Info",width:"20%",render:s=>e.jsxs("div",{className:"space-y-1",children:[s.contact&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(d,{className:"w-3 h-3 mr-1 text-gray-400"}),s.contact]}),s.residence&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(x,{className:"w-3 h-3 mr-1 text-gray-400"}),s.residence]})]})},{key:"joinedDate",header:"Joined Date",width:"15%",align:"center",render:s=>e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"w-4 h-4 text-blue-500"}),e.jsx("span",{className:"font-semibold text-sm",children:m(s.joinedDate)})]}),e.jsxs("span",{className:"text-xs text-gray-500 mt-1",children:[Math.floor(((new Date).getTime()-new Date(s.joinedDate).getTime())/864e5)," days ago"]})]})},...v.map((s=>({key:`attendance_${s}`,header:N(s),width:`${Math.max(8,40/v.length)}%`,align:"center",render:t=>{const a=O(t.id,s);return e.jsx("div",{className:"flex justify-center",children:e.jsx(T,{memberId:t.id,date:s,currentStatus:a,onMarkAttendance:(e,s,t)=>g(e,s,t)})})}}))),{key:"attendanceRate",header:"Attendance",width:"10%",align:"center",render:s=>{const t=L(s.id);return e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("span",{className:"font-semibold text-lg",children:[t,"%"]}),e.jsx(S,{variant:t>=80?"success":t>=60?"warning":"danger",size:"sm",className:"mt-1",children:t>=80?"Excellent":t>=60?"Good":"Poor"})]})}},{key:"actions",header:"Actions",width:"10%",align:"center",render:s=>e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(i,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),h(s)},className:"p-2 hover:bg-blue-100",title:"Edit New Believer",children:e.jsx(l,{className:"w-4 h-4 text-blue-600"})}),e.jsx(i,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),u(s.id)},className:"p-2 hover:bg-red-100",title:"Delete New Believer",children:e.jsx(c,{className:"w-4 h-4 text-red-600"})})]})}],I=(new Date).toLocaleDateString("en-US",{month:"long",year:"numeric"});return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[e.jsx(n,{className:"w-5 h-5 mr-2 text-green-600"}),"New Believers Table - ",I]}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Attendance tracking for ",v.length," Sunday",1!==v.length?"s":""," this month"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(p,{placeholder:"Search new believers...",value:f,onChange:e=>y(e.target.value),className:"border-0 bg-white/50 focus:bg-white/80 transition-colors"}),e.jsxs("select",{value:w,onChange:e=>b(e.target.value),className:"px-3 py-2 border-0 bg-white/50 focus:bg-white/80 rounded-lg transition-colors",children:[e.jsx("option",{value:"",children:"All Ministries"}),M.map((s=>e.jsx("option",{value:s,children:s},s)))]}),e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:B,onChange:e=>C(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"First time only"})]})]})]})}),e.jsx(D,{data:$,columns:E,loading:j,emptyMessage:f||w||B?"No new believers match your filters":"No new believers registered yet",onRowClick:e=>F(e)}),$.length>0&&e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Total New Believers"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:$.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"First Time Visitors"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:$.filter((e=>e.isFirstTime)).length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Avg Attendance"}),e.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[$.length>0?Math.round($.reduce(((e,s)=>e+L(s.id)),0)/$.length):0,"%"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"With Ministry"}),e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:$.filter((e=>e.ministry&&""!==e.ministry.trim())).length})]})]})}),e.jsx(A,{isOpen:null!==k,onClose:()=>F(null),newBeliever:k})]})},O=()=>{const{newBelievers:t,openNewBelieverForm:r,isNewBelieverFormOpen:h,editingNewBeliever:u,closeNewBelieverForm:g,deleteNewBelieverHandler:j,showConfirmation:N}=a(),[p,b]=s.useState(""),[B,C]=s.useState(""),[k,S]=s.useState(!1),[D,T]=s.useState("cards"),[F,A]=s.useState(!1),O=s.useMemo((()=>{const e=t.map((e=>e.ministry)).filter((e=>e&&""!==e.trim()));return[...new Set(e)].sort()}),[t]),L=s.useMemo((()=>t.filter((e=>{const s=""===p||e.name.toLowerCase().includes(p.toLowerCase())||e.surname.toLowerCase().includes(p.toLowerCase())||e.contact.toLowerCase().includes(p.toLowerCase()),t=""===B||e.ministry===B,a=!k||e.isFirstTime;return s&&t&&a}))),[t,p,B,k]),P=s.useMemo((()=>[...L].sort(((e,s)=>new Date(s.joinedDate).getTime()-new Date(e.joinedDate).getTime()))),[L]),E=e=>`${e.name}${e.surname?` ${e.surname}`:""}`;return e.jsxs("div",{className:"p-4 space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"New Believers"}),e.jsxs("p",{className:"text-gray-600 mt-1",children:[t.length," new believer",1!==t.length?"s":""," registered"]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[e.jsx("button",{onClick:()=>T("cards"),className:"p-2 rounded-md transition-colors "+("cards"===D?"bg-white text-blue-600 shadow-sm":"text-gray-500 hover:text-gray-700"),title:"Card View",children:e.jsx(v,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>T("table"),className:"p-2 rounded-md transition-colors "+("table"===D?"bg-white text-blue-600 shadow-sm":"text-gray-500 hover:text-gray-700"),title:"Table View",children:e.jsx(f,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(i,{onClick:()=>A(!0),variant:"secondary",leftIcon:e.jsx(y,{className:"w-4 h-4"}),size:"sm",children:"Bulk Add"}),e.jsx(i,{onClick:()=>r(void 0),variant:"primary",leftIcon:e.jsx(y,{className:"w-5 h-5"}),children:"Add New Believer"})]})]})]}),e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),e.jsx("input",{id:"search",type:"text",placeholder:"Search by name or contact...",value:p,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"ministry-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Ministry"}),e.jsxs("select",{id:"ministry-filter",value:B,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All Ministries"}),O.map((s=>e.jsx("option",{value:s,children:s},s)))]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:k,onChange:e=>S(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"First time visitors only"})]})})]})}),"table"===D?e.jsx($,{}):0===P.length?e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center",children:[e.jsx(n,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===t.length?"No New Believers Yet":"No Results Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:0===t.length?"Start by adding your first new believer to track their journey.":"Try adjusting your search or filter criteria."}),0===t.length&&e.jsx(i,{onClick:()=>r(void 0),variant:"primary",leftIcon:e.jsx(y,{className:"w-5 h-5"}),children:"Add First New Believer"})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:P.map((s=>e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:E(s)}),s.isFirstTime&&e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"First Time"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>(e=>{r(e)})(s),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Edit",children:e.jsx(l,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>(e=>{N("deleteNewBeliever",e,(()=>j(e.id)))})(s),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Delete",children:e.jsx(c,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{className:"space-y-2 mb-4",children:[s.contact&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(d,{className:"w-4 h-4 mr-2 text-gray-400"}),s.contact]}),s.residence&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(x,{className:"w-4 h-4 mr-2 text-gray-400"}),s.residence]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(o,{className:"w-4 h-4 mr-2 text-gray-400"}),"Joined: ",m(s.joinedDate)]})]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[s.ministry&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Ministry:"}),e.jsx("span",{className:"ml-2 text-gray-600",children:s.ministry})]}),s.studies&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Studies:"}),e.jsx("span",{className:"ml-2 text-gray-600",children:s.studies})]}),s.occupation&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Occupation:"}),e.jsx("span",{className:"ml-2 text-gray-600",children:s.occupation})]})]})]},s.id)))}),e.jsx(w,{isOpen:h,onClose:g,newBeliever:u}),e.jsx(M,{isOpen:F,onClose:()=>A(!1)})]})};export{O as default};
