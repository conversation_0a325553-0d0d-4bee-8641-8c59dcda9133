import{j as e}from"./utils-DJ-5fhzp.js";import{r as s}from"./charts-DCWOOedL.js";import{u as a,g as t,f as r,a as n,b as l,C as i,c as d,d as c,I as o,U as m,W as x,P as h,H as u,B as g,E as j,T as N,L as b,e as p,S as f}from"./index-DXKcng7s.js";import{M as v}from"./MemberCard-_npCHPLS.js";import{T as y,B as w}from"./Badge-BonlZcKp.js";import"./vendor-CMmhtoO5.js";import"./icons-CQztrb4S.js";import"./AttendanceMarker-TEPqVtRg.js";const C=({bacentaFilter:b})=>{const{members:p,bacentas:f,criticalMemberIds:v,openMemberForm:C,deleteMemberHandler:M,attendanceRecords:k,markAttendanceHandler:A,clearAttendanceHandler:F,isLoading:L}=a(),[P,D]=s.useState(""),[Y,S]=s.useState(new Date),T=s.useMemo((()=>t(Y.getFullYear(),Y.getMonth())),[Y]),I=(e,s)=>{const a=k.find((a=>a.memberId===e&&a.date===s));return a?.status},B=e=>{const s=new Date,a=r(s),t=new Date(e+"T00:00:00"),n=s.getMonth(),l=s.getFullYear(),i=t.getMonth(),d=t.getFullYear();return!(d<l||d===l&&i<n)&&!(e>a)},z=async(e,s)=>{if(!B(s))return;const a=I(e,s);a?"Present"===a?await A(e,s,"Absent"):"Absent"===a&&await F(e,s):await A(e,s,"Present")},R=s.useMemo((()=>p.filter((e=>{if(b&&e.bacentaId!==b)return!1;if(P){const s=P.toLowerCase();return e.firstName.toLowerCase().includes(s)||e.lastName.toLowerCase().includes(s)||e.phoneNumber.includes(P)||e.buildingAddress.toLowerCase().includes(s)}return!0})).sort(((e,s)=>e.lastName.localeCompare(s.lastName)||e.firstName.localeCompare(s.firstName)))),[p,b,P]),$=e=>{if(!e)return"Unassigned";const s=f.find((s=>s.id===e));return s?.name||"Unknown"},H=s.useMemo((()=>[...[{key:"name",header:"Name",width:"200px",render:s=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center",children:e.jsx(m,{className:"w-4 h-4 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-semibold text-gray-900",children:[s.firstName," ",s.lastName]}),v.includes(s.id)&&e.jsxs(w,{variant:"danger",size:"sm",className:"mt-1",children:[e.jsx(x,{className:"w-3 h-3 mr-1"}),"Critical"]})]})]})},{key:"phoneNumber",header:"Phone",width:"140px",render:s=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(h,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-sm",children:s.phoneNumber||"-"})]})},{key:"buildingAddress",header:"Address",width:"180px",render:s=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"truncate text-sm",title:s.buildingAddress,children:s.buildingAddress||"-"})]})},{key:"bacentaId",header:"Bacenta",width:"120px",render:s=>e.jsx(w,{variant:"secondary",size:"sm",children:$(s.bacentaId)})},{key:"bornAgainStatus",header:"Born Again",width:"100px",align:"center",render:s=>e.jsx(w,{variant:s.bornAgainStatus?"success":"warning",size:"sm",children:s.bornAgainStatus?"Yes":"No"})},{key:"joinedDate",header:"Joined",width:"100px",render:s=>e.jsx("div",{className:"text-sm",children:s.joinedDate?n(s.joinedDate):"-"})}],...T.map((s=>{const a=B(s),t=new Date;r(t);const l=new Date(s+"T00:00:00");return l.getFullYear()<t.getFullYear()||l.getFullYear()===t.getFullYear()&&(l.getMonth(),t.getMonth()),{key:`attendance_${s}`,header:e.jsxs("div",{className:"flex flex-col items-center space-y-1",children:[e.jsx("span",{className:"text-xs "+(a?"text-gray-700":"text-gray-400"),children:n(s)}),!1]}),width:"80px",align:"center",render:a=>{const t=I(a.id,s),l="Present"===t,i=B(s),d=new Date;r(d);const c=new Date(s+"T00:00:00"),o=c.getFullYear()<d.getFullYear()||c.getFullYear()===d.getFullYear()&&c.getMonth()<d.getMonth();return e.jsx("div",{className:"flex justify-center",children:e.jsxs("div",{className:"w-6 h-6 rounded border-2 flex items-center justify-center transition-all duration-200 "+(i?l?"bg-green-500 border-green-500 text-white hover:bg-green-600 cursor-pointer":"Absent"===t?"bg-red-500 border-red-500 text-white hover:bg-red-600 cursor-pointer":"bg-gray-100 border-gray-300 text-gray-400 hover:bg-gray-200 cursor-pointer":o?"bg-gray-200 border-gray-300 cursor-not-allowed opacity-60":"bg-blue-50 border-blue-200 cursor-not-allowed opacity-60"),onClick:i?e=>{e.stopPropagation(),z(a.id,s)}:void 0,title:i?`Click to ${t?"Present"===t?"mark absent":"clear attendance":"mark present"} for ${n(s)}`:o?`Past month - cannot edit ${n(s)}`:`Future date - cannot edit ${n(s)}`,children:[l&&e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Absent"===t&&e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})}),!1,!1]})})}}})),{key:"actions",header:"Actions",width:"80px",align:"center",render:s=>e.jsxs("div",{className:"flex items-center justify-center space-x-1",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),C(s)},className:"p-1 hover:bg-blue-100",title:"Edit member",children:e.jsx(j,{className:"w-3 h-3 text-blue-600"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),M(s.id)},className:"p-1 hover:bg-red-100",title:"Delete member",children:e.jsx(N,{className:"w-3 h-3 text-red-600"})})]})}]),[T,v,k,$,C,M,I,z]),E=l(Y.getMonth()),U=Y.getFullYear();return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(i,{className:"w-5 h-5 text-blue-600"}),e.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:["Attendance for ",E," ",U]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:()=>{S((e=>{const s=new Date(e);return s.setMonth(s.getMonth()-1),s}))},className:"group flex items-center space-x-2 px-3 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Previous month",children:[e.jsx(d,{className:"w-4 h-4 text-gray-600 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"hidden sm:inline text-sm font-medium text-gray-700",children:"Previous"})]}),e.jsxs("button",{onClick:()=>{S((e=>{const s=new Date(e);return s.setMonth(s.getMonth()+1),s}))},className:"group flex items-center space-x-2 px-3 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Next month",children:[e.jsx("span",{className:"hidden sm:inline text-sm font-medium text-gray-700",children:"Next"}),e.jsx(c,{className:"w-4 h-4 text-gray-600 group-hover:translate-x-1 transition-transform"})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0",children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:[T.length," Sunday",1!==T.length?"s":""," in ",E]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded border"}),e.jsx("span",{className:"text-gray-600",children:"Editable"})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-50 border border-blue-200 rounded"}),e.jsx("span",{className:"text-gray-600",children:"Future"})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-gray-200 border border-gray-300 rounded"}),e.jsx("span",{className:"text-gray-600",children:"Past Month"})]})]})]}),e.jsx("div",{className:"w-full sm:w-64",children:e.jsx(o,{placeholder:"Search members...",value:P,onChange:e=>D(e.target.value),className:"border-0 bg-white/50 focus:bg-white/80 transition-colors"})})]})]})}),e.jsx("div",{className:"glass rounded-2xl overflow-hidden shadow-2xl",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsx(y,{data:R,columns:H,loading:L,emptyMessage:b?"No members found in this bacenta":P?"No members match your search":"No members added yet",onRowClick:e=>C(e),className:"min-w-full"})})}),R.length>0&&e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Total Members"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:R.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Critical Alerts"}),e.jsx("div",{className:"text-2xl font-bold text-red-600",children:v.filter((e=>R.some((s=>s.id===e)))).length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Sundays This Month"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:T.length})]})]})})]})},M=({bacentaFilter:t})=>{const{members:r,isLoading:n,criticalMemberIds:m,bacentas:x,currentTab:h,displayedDate:u,navigateToPreviousMonth:g,navigateToNextMonth:j}=a(),[N,y]=s.useState(""),[w,M]=s.useState("table"),k=s.useMemo((()=>r.filter((e=>{if(t&&e.bacentaId!==t)return!1;if(N){const s=N.toLowerCase();return e.firstName.toLowerCase().includes(s)||e.lastName.toLowerCase().includes(s)||e.phoneNumber.toLowerCase().includes(s)||e.bacentaId&&x.find((s=>s.id===e.bacentaId))?.name.toLowerCase().includes(s)||e.buildingAddress.toLowerCase().includes(s)}return!0})).sort(((e,s)=>e.lastName.localeCompare(s.lastName)||e.firstName.localeCompare(s.firstName)))),[r,t,N,x]);if(n&&!r.length)return e.jsxs("div",{className:"flex flex-col items-center justify-center py-10",children:[e.jsx(b,{className:"w-10 h-10 text-blue-500"}),e.jsx("p",{className:"mt-2 text-gray-500",children:"Loading members..."})]});const A=l(u.getMonth()),F=u.getFullYear();return e.jsxs("div",{className:"animate-fade-in",children:[e.jsxs("div",{className:"mb-8 glass p-6 shadow-2xl rounded-2xl",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-6",children:[e.jsxs("div",{className:"text-center sm:text-left",children:[e.jsxs("h2",{className:"text-3xl font-bold gradient-text flex items-center justify-center sm:justify-start font-serif",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center mr-3 floating",children:e.jsx(p,{className:"w-6 h-6 text-white"})}),h.name]}),e.jsxs("div",{className:"flex items-center justify-center sm:justify-start space-x-2 mt-2",children:[e.jsx("span",{className:"text-2xl",children:"👥"}),e.jsxs("p",{className:"text-lg font-medium text-gray-600",children:[k.length," member(s) found"]})]})]}),e.jsxs("div",{className:"mt-4 sm:mt-0 flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4",children:[e.jsx("div",{className:"w-full sm:w-auto sm:max-w-sm",children:e.jsxs("div",{className:"relative group",children:[e.jsx(o,{type:"text",placeholder:"Search members...",value:N,onChange:e=>y(e.target.value),className:"pl-12 pr-4 py-3 glass border-0 rounded-xl text-gray-700 placeholder-gray-500 focus:ring-2 focus:ring-gray-500/50 transition-all duration-200",wrapperClassName:"relative mb-0","aria-label":"Search members"}),e.jsx(f,{className:"w-5 h-5 text-gray-500 absolute left-4 top-1/2 transform -translate-y-1/2 group-hover:scale-110 transition-transform"}),N&&e.jsx("button",{onClick:()=>y(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:"✕"})]})}),e.jsxs("div",{className:"flex items-center glass rounded-xl p-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>M("cards"),className:"flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 "+("cards"===w?"bg-white shadow-md text-gray-900":"text-gray-600 hover:text-gray-900"),children:[e.jsxs("div",{className:"w-4 h-4 grid grid-cols-1 gap-1",children:[e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"})]}),e.jsx("span",{className:"text-sm font-medium",children:"Cards"})]}),e.jsxs("button",{onClick:()=>M("table"),className:"flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 "+("table"===w?"bg-white shadow-md text-gray-900":"text-gray-600 hover:text-gray-900"),children:[e.jsxs("div",{className:"w-4 h-4 grid grid-cols-3 gap-0.5",children:[e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"}),e.jsx("div",{className:"bg-current rounded-sm"})]}),e.jsx("span",{className:"text-sm font-medium",children:"Table"})]})]})]})]}),"cards"===w&&e.jsxs("div",{className:"mt-6 pt-6 border-t border-white/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:e.jsx(i,{className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold gradient-text",children:"Attendance Period"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[A," ",F]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:g,className:"group flex items-center space-x-2 px-4 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Previous month for attendance",children:[e.jsx(d,{className:"w-5 h-5 text-gray-600 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"hidden sm:inline font-medium text-gray-700",children:"Previous"})]}),e.jsxs("button",{onClick:j,className:"group flex items-center space-x-2 px-4 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Next month for attendance",children:[e.jsx("span",{className:"hidden sm:inline font-medium text-gray-700",children:"Next"}),e.jsx(c,{className:"w-5 h-5 text-gray-600 group-hover:translate-x-1 transition-transform"})]})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-gray-100 to-gray-200 p-3 rounded-xl",children:e.jsxs("p",{className:"text-sm text-gray-700 text-center",children:["📊 Attendance data shown for ",e.jsxs("span",{className:"font-semibold",children:[A," ",F]})]})})]})]}),0===k.length&&!n&&e.jsxs("div",{className:"glass p-12 rounded-2xl text-center shadow-2xl animate-fade-in",children:[e.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-6 floating",children:e.jsx(p,{className:"w-12 h-12 text-gray-400"})}),e.jsx("h3",{className:"text-2xl font-bold gradient-text mb-3",children:"No Members Found"}),N&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-gray-600",children:['No results for "',e.jsx("span",{className:"font-semibold text-primary-600",children:N}),'"']}),e.jsx("p",{className:"text-sm text-gray-500",children:"Try adjusting your search terms"})]}),!t&&!N&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-gray-600",children:"Ready to build your community?"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Add your first member using the floating action button"})]}),t&&!N&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-gray-600",children:"This Bacenta is waiting for members"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Add members to get started with attendance tracking"})]})]}),"cards"===w?e.jsx("div",{className:"space-y-6",children:k.map(((s,a)=>e.jsx("div",{className:"animate-fade-in",style:{animationDelay:.1*a+"s"},children:e.jsx(v,{member:s,isCritical:m.includes(s.id)})},s.id)))}):e.jsx("div",{className:"animate-fade-in",children:e.jsx(C,{bacentaFilter:t})})]})};export{M as default};
