
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>SAT Mobile</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⛪</text></svg>">
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#334155">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="SAT Mobile">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="application-name" content="SAT Mobile">
  <link rel="apple-touch-icon" href="/icon-192.svg">
  <link rel="apple-touch-icon" sizes="192x192" href="/icon-192.svg">
  <link rel="apple-touch-icon" sizes="512x512" href="/icon-512.svg">

  <!-- Mobile app back button handling -->
  <script>
    // Prevent default back button behavior on mobile
    document.addEventListener('DOMContentLoaded', function() {
      // Add initial history state to prevent app closure
      if (window.history.length === 1) {
        window.history.pushState({ page: 'app' }, '', window.location.href);
      }

      // Handle mobile back button
      window.addEventListener('popstate', function(event) {
        // This will be handled by the React app's navigation system
        // The event is prevented in the useNavigation hook
      });

      // Prevent context menu on long press (mobile)
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
      });

      // Prevent text selection on mobile
      document.addEventListener('selectstart', function(e) {
        if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
          e.preventDefault();
        }
      });
    });
  </script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
            'serif': ['Playfair Display', 'serif'],
          },
          colors: {
            primary: {
              50: '#f8fafc',
              100: '#f1f5f9',
              200: '#e2e8f0',
              300: '#cbd5e1',
              400: '#94a3b8',
              500: '#64748b',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
            },
            secondary: {
              50: '#f9fafb',
              100: '#f3f4f6',
              200: '#e5e7eb',
              300: '#d1d5db',
              400: '#9ca3af',
              500: '#6b7280',
              600: '#4b5563',
              700: '#374151',
              800: '#1f2937',
              900: '#111827',
            },
            accent: {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            }
          },
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.3s ease-out',
            'slide-down': 'slideDown 0.3s ease-out',
            'scale-in': 'scaleIn 0.2s ease-out',
            'bounce-gentle': 'bounceGentle 0.6s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'float': 'float 3s ease-in-out infinite',
            'shimmer': 'shimmer 2s linear infinite',
            'gradient': 'gradient 15s ease infinite',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0', transform: 'translateY(10px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            slideUp: {
              '0%': { transform: 'translateY(100%)' },
              '100%': { transform: 'translateY(0)' },
            },
            slideDown: {
              '0%': { transform: 'translateY(-100%)' },
              '100%': { transform: 'translateY(0)' },
            },
            scaleIn: {
              '0%': { transform: 'scale(0.9)', opacity: '0' },
              '100%': { transform: 'scale(1)', opacity: '1' },
            },
            bounceGentle: {
              '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
              '40%, 43%': { transform: 'translate3d(0,-8px,0)' },
              '70%': { transform: 'translate3d(0,-4px,0)' },
              '90%': { transform: 'translate3d(0,-2px,0)' },
            },
            float: {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-10px)' },
            },
            shimmer: {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' },
            },
            gradient: {
              '0%, 100%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
            },
          },
          backgroundSize: {
            '300%': '300%',
          },
          backdropBlur: {
            xs: '2px',
          },
        }
      }
    }
  </script>
  <style>
    body {
      margin: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      background: linear-gradient(-45deg, #f8fafc, #f1f5f9, #e2e8f0, #cbd5e1);
      background-size: 400% 400%;
      animation: gradient 30s ease infinite;
    }

    /* Enhanced scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    ::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, #64748b, #475569);
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(45deg, #475569, #334155);
    }

    /* Glass morphism utility */
    .glass {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .glass-dark {
      background: rgba(0, 0, 0, 0.25);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
    }

    /* Optimized transitions - only for specific properties */
    button, .interactive-card, .glass {
      transition: transform 0.2s ease, opacity 0.2s ease, box-shadow 0.2s ease;
    }

    /* Prevent horizontal scroll on mobile */
    html, body {
      overflow-x: hidden;
      width: 100%;
      max-width: 100vw;
    }

    /* Ensure proper box sizing */
    *, *::before, *::after {
      box-sizing: border-box;
    }

    /* Custom gradient text */
    .gradient-text {
      background: linear-gradient(45deg, #334155, #1e293b);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Floating animation */
    .floating {
      animation: float 3s ease-in-out infinite;
    }

    /* Shimmer effect */
    .shimmer {
      position: relative;
      overflow: hidden;
    }

    .shimmer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: shimmer 2s linear infinite;
    }
  </style>

<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
