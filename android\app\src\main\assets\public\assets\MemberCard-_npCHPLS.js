import{j as e}from"./utils-DJ-5fhzp.js";import{u as s,U as t,W as r,E as a,T as l,P as n,H as d,C as i,a as c,f as x}from"./index-DXKcng7s.js";import{A as o}from"./AttendanceMarker-TEPqVtRg.js";const m=({member:m,isCritical:h})=>{const{displayedSundays:g,attendanceRecords:u,markAttendanceHandler:j,deleteMemberHandler:b,openMemberForm:p,bacentas:f,criticalMemberIds:N}=s(),v=e=>{const s=u.find((s=>s.memberId===m.id&&s.date===e));return s?.status},y=()=>{const e=g.length;if(0===e)return 0;const s=g.filter((e=>"Present"===v(e))).length;return Math.round(s/e*100)},w=f.find((e=>e.id===m.bacentaId)),A=e=>{if(!e||"Invalid Date"===e)return"N/A";try{return new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"numeric",day:"numeric"})}catch(s){return"Invalid Date"}};return e.jsxs("div",{className:"group glass shadow-2xl rounded-2xl p-6 mb-6 border-l-4 transition-all duration-300 relative overflow-hidden animate-fade-in "+(h?"border-red-500":"border-gray-500"),children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),h&&e.jsx("div",{className:"absolute inset-0 bg-red-500/5"}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between relative z-10",children:[e.jsxs("div",{className:"flex items-start mb-4 sm:mb-0 flex-1",children:[e.jsxs("div",{className:"relative p-3 rounded-2xl mr-4 shadow-lg floating "+(h?"bg-gradient-to-br from-red-100 to-red-200":"bg-gradient-to-br from-gray-100 to-gray-200"),children:[e.jsx(t,{className:"w-10 h-10 "+(h?"text-red-600":"text-gray-600")}),h&&e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-bounce-gentle"})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsxs("h3",{className:"text-xl font-bold text-gray-800 group-hover:text-gray-900 transition-colors",children:[m.firstName," ",m.lastName]}),h&&e.jsxs("div",{className:"flex items-center space-x-1 bg-red-100 px-2 py-1 rounded-full",children:[e.jsx(r,{className:"w-4 h-4 text-red-500"}),e.jsx("span",{className:"text-xs font-bold text-red-600",children:"CRITICAL"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"📍"}),e.jsx("p",{className:"text-sm font-medium text-gray-600",children:w?.name||e.jsx("span",{className:"italic text-gray-400",children:"Unassigned"})})]}),m.bornAgainStatus&&e.jsxs("div",{className:"inline-flex items-center space-x-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm",children:[e.jsx("span",{children:"✨"}),e.jsx("span",{children:"Born Again"})]})]})]}),e.jsxs("div",{className:"flex space-x-2 mt-2 sm:mt-0 self-start sm:self-center",children:[e.jsxs("button",{onClick:()=>p(m),className:"group/btn flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-800 rounded-xl transition-all duration-200 shadow-sm","aria-label":"Edit Member",children:[e.jsx(a,{className:"w-4 h-4 transition-colors"}),e.jsx("span",{className:"hidden sm:inline font-medium",children:"Edit"})]}),e.jsxs("button",{onClick:()=>b(m.id),className:"group/btn flex items-center space-x-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 hover:text-red-800 rounded-xl transition-all duration-200 shadow-sm","aria-label":"Delete Member",children:[e.jsx(l,{className:"w-4 h-4 transition-colors"}),e.jsx("span",{className:"hidden sm:inline font-medium",children:"Delete"})]})]})]}),h&&e.jsx("div",{className:"mt-4 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-xl text-sm text-red-700 animate-fade-in",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-red-200 rounded-full flex items-center justify-center",children:e.jsx(r,{className:"w-5 h-5 text-red-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold",children:"Follow-up Required"}),e.jsxs("p",{className:"text-xs text-red-600",children:["This member has ",N.includes(m.id)?"2 or more":""," consecutive absences for the displayed month."]})]})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{className:"glass p-4 rounded-xl hover:scale-102 transition-transform duration-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center",children:e.jsx(n,{className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 font-medium uppercase tracking-wide",children:"Phone"}),e.jsx("p",{className:"font-semibold text-gray-700",children:(e=>{if(!e)return"N/A";const s=(""+e).replace(/\D/g,""),t=s.match(/^(\d{3})(\d{3})(\d{4})$/);return t?`(${t[1]}) ${t[2]}-${t[3]}`:s.length>6?`${s.slice(0,3)}-${s.slice(3,6)}-${s.slice(6)}`:e})(m.phoneNumber)||e.jsx("span",{className:"text-gray-400 italic",children:"No phone"})})]})]})}),e.jsx("div",{className:"glass p-4 rounded-xl hover:scale-102 transition-transform duration-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center",children:e.jsx(d,{className:"w-5 h-5 text-green-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 font-medium uppercase tracking-wide",children:"Address"}),e.jsx("p",{className:"font-semibold text-gray-700",children:m.buildingAddress||e.jsx("span",{className:"text-gray-400 italic",children:"No address"})})]})]})}),e.jsx("div",{className:"glass p-4 rounded-xl hover:scale-102 transition-transform duration-200 md:col-span-2",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:e.jsx(i,{className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 font-medium uppercase tracking-wide",children:"Member Since"}),e.jsx("p",{className:"font-semibold text-gray-700",children:m.joinedDate?c(m.joinedDate):e.jsx("span",{className:"text-gray-400 italic",children:"N/A"})})]})]})})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-white/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h4",{className:"text-lg font-bold gradient-text flex items-center",children:[e.jsx("span",{className:"text-xl mr-2",children:"📊"}),"Attendance Record"]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"text-sm text-gray-500 bg-white/50 px-3 py-1 rounded-full",children:[g.length," Services"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[y(),"% Present"]})]})]}),g.length>0?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"glass p-4 rounded-xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Monthly Overview"}),e.jsx("span",{className:"text-sm font-bold text-gray-700",children:(()=>{const e=g.length,s=g.filter((e=>"Present"===v(e))).length,t=g.filter((e=>"Absent"===v(e))).length;return`${s} Present • ${t} Absent • ${e-s-t} Unmarked`})()})]}),e.jsx("div",{className:"w-full h-3 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-1000 ease-out",style:{width:`${y()}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-2",children:[e.jsx("span",{children:"0%"}),e.jsx("span",{children:"50%"}),e.jsx("span",{children:"100%"})]})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:g.map(((s,t)=>{const r=v(s),a=(e=>{const s=new Date,t=x(s),r=new Date(e+"T00:00:00"),a=s.getMonth(),l=s.getFullYear(),n=r.getMonth(),d=r.getFullYear();return!(d<l||d===l&&n<a||e>t)})(s),l=new Date,n=x(l),d=new Date(s+"T00:00:00"),i=s>n,h=d.getFullYear()<l.getFullYear()||d.getFullYear()===l.getFullYear()&&d.getMonth()<l.getMonth();return e.jsxs("div",{className:"glass p-4 rounded-xl transition-all duration-200 animate-fade-in border-l-4 relative "+(a?"Present"===r?"border-green-500 bg-green-50/50 hover:scale-102":"Absent"===r?"border-red-500 bg-red-50/50 hover:scale-102":"border-gray-300 hover:scale-102":h?"border-gray-400 bg-gray-50/50 opacity-75":"border-blue-400 bg-blue-50/50 opacity-75"),style:{animationDelay:.1*t+"s"},children:[!1,e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-semibold "+(a?"text-gray-700":"text-gray-500"),children:c(s)}),e.jsxs("p",{className:"text-xs font-medium "+(a?"Present"===r?"text-green-600":"Absent"===r?"text-red-600":"text-gray-500":"text-gray-400"),children:[r||"Not Marked",!a&&e.jsx("span",{className:"ml-1 text-xs",children:i?"(Future)":"(Past Month)"})]})]}),e.jsx("div",{className:"flex items-center space-x-1",children:e.jsx(o,{memberId:m.id,date:s,currentStatus:r,onMarkAttendance:j})})]})]},s)}))})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-3xl",children:"📅"})}),e.jsx("h5",{className:"text-lg font-semibold text-gray-600 mb-2",children:"No Services Scheduled"}),e.jsx("p",{className:"text-sm text-gray-500",children:"No Sundays scheduled for the selected month"})]})]}),e.jsxs("div",{className:"mt-6 pt-4 border-t border-white/10 flex items-center justify-between text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{children:["Created: ",A(m.createdDate)]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:["Updated: ",A(m.lastUpdated)]})]}),e.jsxs("div",{className:"text-gray-500",children:["ID: ",m.id.slice(-6)]})]})]})};export{m as M};
