# Firebase Configuration
# Copy this file to .env and fill in your actual Firebase project values

# Firebase API Key
REACT_APP_FIREBASE_API_KEY=your-api-key-here

# Firebase Auth Domain
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com

# Firebase Project ID
REACT_APP_FIREBASE_PROJECT_ID=your-project-id

# Firebase Storage Bucket
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com

# Firebase Messaging Sender ID
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789

# Firebase App ID
REACT_APP_FIREBASE_APP_ID=your-app-id

# Development Settings
# Set to 'true' to use Firebase emulators in development
REACT_APP_USE_FIREBASE_EMULATOR=false

# Existing Gemini API Key (if used)
GEMINI_API_KEY=your-gemini-api-key-here
