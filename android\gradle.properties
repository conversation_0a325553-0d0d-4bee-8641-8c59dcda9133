# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# SSL and network configuration workarounds for corporate networks
systemProp.javax.net.ssl.trustStore=NONE
systemProp.javax.net.ssl.trustStoreType=Windows-ROOT
systemProp.com.sun.net.ssl.checkRevocation=false
systemProp.sun.security.ssl.allowUnsafeRenegotiation=true
systemProp.sun.security.ssl.allowLegacyHelloMessages=true

# Disable SSL verification (temporary workaround)
systemProp.javax.net.ssl.trustStorePassword=changeit
systemProp.javax.net.ssl.keyStore=NONE
systemProp.javax.net.ssl.keyStorePassword=changeit
