import{j as e}from"./utils-DJ-5fhzp.js";import{u as s,L as t,b as a,W as r,i as l,C as i,c as n,d}from"./index-DXKcng7s.js";import{M as c}from"./MemberCard-_npCHPLS.js";import"./charts-DCWOOedL.js";import"./vendor-CMmhtoO5.js";import"./icons-CQztrb4S.js";import"./AttendanceMarker-TEPqVtRg.js";const o=()=>{const{members:o,criticalMemberIds:m,isLoading:x,displayedDate:h,navigateToPreviousMonth:g,navigateToNextMonth:f,displayedSundays:j}=s(),p=o.filter((e=>m.includes(e.id))).sort(((e,s)=>e.lastName.localeCompare(s.lastName)||e.firstName.localeCompare(s.firstName)));if(x&&!p.length&&!j.length)return e.jsx("div",{className:"flex flex-col items-center justify-center py-16 animate-fade-in",children:e.jsx("div",{className:"glass p-8 rounded-3xl shadow-2xl",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(t,{className:"w-12 h-12 text-red-500"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-red-200 rounded-full"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-lg font-semibold text-red-600",children:"Loading Critical Alerts..."}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Analyzing attendance patterns"})]})]})})});const u=a(h.getMonth()),N=h.getFullYear();return e.jsxs("div",{className:"animate-fade-in",children:[e.jsxs("div",{className:"mb-8 glass p-8 shadow-2xl rounded-2xl border-l-4 border-red-500 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-red-50/50 to-orange-50/50 animate-pulse-slow"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg floating",children:e.jsx(r,{className:"w-7 h-7 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold gradient-text font-serif",children:"Critical Alerts"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Members requiring immediate follow-up"})]})]}),p.length>0&&e.jsxs("div",{className:"bg-red-100 text-red-700 px-4 py-2 rounded-full font-bold text-lg animate-bounce-gentle",children:[p.length," Alert",1!==p.length?"s":""]})]}),e.jsx("div",{className:"bg-gradient-to-r from-red-100 to-orange-100 p-4 rounded-xl",children:e.jsx("p",{className:"text-red-700 font-medium",children:p.length>0?`⚠️ ${p.length} member(s) flagged with ${l} or more consecutive absences in ${u} ${N}.`:`✅ No members flagged as critical for ${u} ${N}.`})})]})]}),e.jsxs("div",{className:"mb-8 glass p-6 shadow-2xl rounded-2xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:e.jsx(i,{className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold gradient-text",children:"Analysis Period"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[u," ",N]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:g,className:"group flex items-center space-x-2 px-4 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Previous month for attendance",children:[e.jsx(n,{className:"w-5 h-5 text-gray-600 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"hidden sm:inline font-medium text-gray-700",children:"Previous"})]}),e.jsxs("button",{onClick:f,className:"group flex items-center space-x-2 px-4 py-2 glass hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105","aria-label":"Next month for attendance",children:[e.jsx("span",{className:"hidden sm:inline font-medium text-gray-700",children:"Next"}),e.jsx(d,{className:"w-5 h-5 text-gray-600 group-hover:translate-x-1 transition-transform"})]})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-gray-100 to-gray-200 p-3 rounded-xl",children:e.jsxs("p",{className:"text-sm text-gray-700 text-center",children:["📊 Critical status based on attendance records for ",e.jsxs("span",{className:"font-semibold",children:[u," ",N]})]})})]}),0===p.length&&!x&&e.jsxs("div",{className:"glass p-12 rounded-2xl text-center shadow-2xl animate-fade-in",children:[e.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-green-200 to-green-300 rounded-full flex items-center justify-center mx-auto mb-6 floating",children:e.jsx("div",{className:"text-4xl",children:"🎉"})}),e.jsx("h3",{className:"text-3xl font-bold gradient-text mb-4 font-serif",children:"Excellent News!"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("p",{className:"text-xl text-gray-700",children:["No critical members for ",u," ",N]}),e.jsx("p",{className:"text-gray-600",children:"All members have maintained good attendance patterns"}),e.jsx("div",{className:"bg-gradient-to-r from-green-100 to-green-200 p-4 rounded-xl mt-6",children:e.jsx("p",{className:"text-green-700 font-medium",children:"✨ Keep up the great work in building a committed community!"})})]})]}),e.jsx("div",{className:"space-y-6",children:p.map(((s,t)=>e.jsx("div",{className:"animate-fade-in",style:{animationDelay:.1*t+"s"},children:e.jsx(c,{member:s,isCritical:!0})},s.id)))})]})};export{o as default};
