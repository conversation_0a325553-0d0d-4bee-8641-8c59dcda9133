import{j as e}from"./utils-DJ-5fhzp.js";import{R as t}from"./charts-DCWOOedL.js";function r({data:r,columns:s,onRowClick:n,onRowLongPress:a,className:l="",emptyMessage:i="No data available",loading:o=!1,striped:d=!0,hoverable:c=!0}){const[x,m]=t.useState(null),[u,g]=t.useState(null),p=e=>{switch(e){case"center":return"text-center";case"right":return"text-right";default:return"text-left"}},h=()=>{x&&(clearTimeout(x),m(null),g(null))},b=()=>{x&&(clearTimeout(x),m(null),g(null))};return o?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Loading..."})]}):0===r.length?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx("span",{className:"text-2xl",children:"📋"})}),e.jsx("p",{className:"text-gray-500 text-lg",children:i})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:`w-full min-w-full ${l}`,children:[e.jsx("thead",{children:e.jsx("tr",{className:"bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200",children:s.map(((t,r)=>e.jsx("th",{className:`px-2 sm:px-3 py-2 sm:py-3 text-xs font-semibold text-gray-700 uppercase tracking-wider ${p(t.align)}`,style:{width:t.width,minWidth:t.width||"80px"},children:e.jsx("div",{className:"truncate",children:t.header})},r)))})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:r.map(((t,r)=>e.jsx("tr",{className:`\n                ${d&&r%2==0?"bg-white":"bg-gray-50/30"}\n                ${c?"hover:bg-blue-50/50 transition-colors duration-200":""}\n                ${n||a?"cursor-pointer":""}\n                ${u===t?"bg-blue-100":""}\n                select-none\n              `,onClick:()=>(e=>{!x&&n&&n(e)})(t),onTouchStart:e=>((e,t)=>{if(!a)return;const r=setTimeout((()=>{a(e,t),navigator.vibrate&&navigator.vibrate(50)}),500);m(r),g(e)})(t,e),onTouchEnd:h,onMouseDown:e=>((e,t)=>{if(!a)return;const r=setTimeout((()=>{a(e,t)}),500);m(r),g(e)})(t,e),onMouseUp:b,onMouseLeave:b,children:s.map(((r,s)=>{const n=((e,t)=>"string"==typeof t.key&&t.key.includes(".")?t.key.split(".").reduce(((e,t)=>e?.[t]),e):e[t.key])(t,r);return e.jsx("td",{className:`px-2 sm:px-3 py-2 sm:py-3 text-xs sm:text-sm text-gray-900 ${p(r.align)}`,style:{width:r.width,minWidth:r.width||"80px"},children:e.jsx("div",{className:"truncate",children:r.render?r.render(t,n):n||"-"})},s)}))},r)))})]})})}const s=({children:t,color:r="gray",size:s="md",className:n=""})=>e.jsx("span",{className:`inline-flex items-center font-medium rounded-full ${{gray:"bg-gray-100 text-gray-800",red:"bg-red-100 text-red-800",yellow:"bg-yellow-100 text-yellow-800",green:"bg-green-100 text-green-800",blue:"bg-blue-100 text-blue-800",indigo:"bg-indigo-100 text-indigo-800",purple:"bg-purple-100 text-purple-800",pink:"bg-pink-100 text-pink-800"}[r]} ${{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-0.5 text-sm"}[s]} ${n}`,children:t});export{s as B,r as T};
