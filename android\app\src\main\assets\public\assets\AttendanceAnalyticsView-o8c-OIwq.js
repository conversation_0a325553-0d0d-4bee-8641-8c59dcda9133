import{j as s}from"./utils-DJ-5fhzp.js";import{C as e,a,L as t,B as r,b as i,P as n,A as c,p as l,c as d,d as o,i as m}from"./charts-DCWOOedL.js";import"./index-DXKcng7s.js";import"./vendor-CMmhtoO5.js";import"./icons-CQztrb4S.js";e.register(a,t,r,i,n,c,l,d,o,m);const x=()=>s.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"📊 Attendance Analytics"}),s.jsx("p",{className:"text-gray-600 text-lg",children:"Analytics dashboard is working!"}),s.jsx("div",{className:"mt-4 p-4 bg-green-100 rounded-lg",children:s.jsx("p",{className:"text-green-800",children:"✅ Component loaded successfully"})})]})});export{x as default};
