import{r as e}from"./charts-DCWOOedL.js";var t,r,n={exports:{}},i={};var s=(r||(r=1,n.exports=function(){if(t)return i;t=1;var e=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function n(t,r,n){var i=null;if(void 0!==n&&(i=""+n),void 0!==r.key&&(i=""+r.key),"key"in r)for(var s in n={},r)"key"!==s&&(n[s]=r[s]);else n=r;return r=n.ref,{$$typeof:e,type:t,key:i,ref:void 0!==r?r:null,props:n}}return i.Fragment=r,i.jsx=n,i.jsxs=n,i}()),n.exports);const a=e.createContext({});function o(t){const r=e.useRef(null);return null===r.current&&(r.current=t()),r.current}const l="undefined"!=typeof window,c=l?e.useLayoutEffect:e.useEffect,h=e.createContext(null);function f(e,t){-1===e.indexOf(t)&&e.push(t)}function u(e,t){const r=e.indexOf(t);r>-1&&e.splice(r,1)}const d=(e,t,r)=>r>t?t:r<e?e:r;const p={},m=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function g(e){return"object"==typeof e&&null!==e}const v=e=>/^0[^.\s]+$/u.test(e);function T(e){let t;return()=>(void 0===t&&(t=e()),t)}const y=e=>e,w=(e,t)=>r=>t(e(r)),E=(...e)=>e.reduce(w),b=(e,t,r)=>{const n=t-e;return 0===n?1:(r-e)/n};class S{constructor(){this.subscriptions=[]}add(e){return f(this.subscriptions,e),()=>u(this.subscriptions,e)}notify(e,t,r){const n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){const n=this.subscriptions[i];n&&n(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const A=e=>1e3*e,x=e=>e/1e3;function C(e,t){return t?e*(1e3/t):0}const O=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function R(e,t,r,n){if(e===t&&r===n)return y;const i=t=>function(e,t,r,n,i){let s,a,o=0;do{a=t+(r-t)/2,s=O(a,n,i)-e,s>0?r=a:t=a}while(Math.abs(s)>1e-7&&++o<12);return a}(t,0,1,e,r);return e=>0===e||1===e?e:O(i(e),t,n)}const _=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,P=e=>t=>1-e(1-t),k=R(.33,1.53,.69,.99),D=P(k),I=_(D),N=e=>(e*=2)<1?.5*D(e):.5*(2-Math.pow(2,-10*(e-1))),M=e=>1-Math.sin(Math.acos(e)),L=P(M),F=_(M),U=R(.42,0,1,1),B=R(0,0,.58,1),V=R(.42,0,.58,1),W=e=>Array.isArray(e)&&"number"==typeof e[0],j={linear:y,easeIn:U,easeInOut:V,easeOut:B,circIn:M,circInOut:F,circOut:L,backIn:D,backInOut:I,backOut:k,anticipate:N},H=e=>{if(W(e)){e.length;const[t,r,n,i]=e;return R(t,r,n,i)}return"string"==typeof e?j[e]:e},G=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function z(e,t){let r=!1,n=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,a=G.reduce(((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,n=!1,i=!1;const s=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function o(t){s.has(t)&&(l.schedule(t),e()),t(a)}const l={schedule:(e,i=!1,a=!1)=>{const o=a&&n?t:r;return i&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{a=e,n?i=!0:(n=!0,[t,r]=[r,t],t.forEach(o),t.clear(),n=!1,i&&(i=!1,l.process(e)))}};return l}(s),e)),{}),{setup:o,read:l,resolveKeyframes:c,preUpdate:h,update:f,preRender:u,render:d,postRender:m}=a,g=()=>{const s=p.useManualTiming?i.timestamp:performance.now();r=!1,p.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(s-i.timestamp,40),1)),i.timestamp=s,i.isProcessing=!0,o.process(i),l.process(i),c.process(i),h.process(i),f.process(i),u.process(i),d.process(i),m.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(g))};return{schedule:G.reduce(((t,s)=>{const o=a[s];return t[s]=(t,s=!1,a=!1)=>(r||(r=!0,n=!0,i.isProcessing||e(g)),o.schedule(t,s,a)),t}),{}),cancel:e=>{for(let t=0;t<G.length;t++)a[G[t]].cancel(e)},state:i,steps:a}}const{schedule:Y,cancel:X,state:K,steps:J}=z("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:y,!0);let q;function Z(){q=void 0}const Q={now:()=>(void 0===q&&Q.set(K.isProcessing||p.useManualTiming?K.timestamp:performance.now()),q),set:e=>{q=e,queueMicrotask(Z)}},ee=e=>t=>"string"==typeof t&&t.startsWith(e),te=ee("--"),re=ee("var(--"),ne=e=>!!re(e)&&ie.test(e.split("/*")[0].trim()),ie=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,se={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},ae={...se,transform:e=>d(0,1,e)},oe={...se,default:1},le=e=>Math.round(1e5*e)/1e5,ce=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const he=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,fe=(e,t)=>r=>Boolean("string"==typeof r&&he.test(r)&&r.startsWith(e)||t&&!function(e){return null==e}(r)&&Object.prototype.hasOwnProperty.call(r,t)),ue=(e,t,r)=>n=>{if("string"!=typeof n)return n;const[i,s,a,o]=n.match(ce);return{[e]:parseFloat(i),[t]:parseFloat(s),[r]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},de={...se,transform:e=>Math.round((e=>d(0,255,e))(e))},pe={test:fe("rgb","red"),parse:ue("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+de.transform(e)+", "+de.transform(t)+", "+de.transform(r)+", "+le(ae.transform(n))+")"};const me={test:fe("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:pe.transform},ge=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ve=ge("deg"),Te=ge("%"),ye=ge("px"),we=ge("vh"),Ee=ge("vw"),be=(()=>({...Te,parse:e=>Te.parse(e)/100,transform:e=>Te.transform(100*e)}))(),Se={test:fe("hsl","hue"),parse:ue("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+Te.transform(le(t))+", "+Te.transform(le(r))+", "+le(ae.transform(n))+")"},Ae={test:e=>pe.test(e)||me.test(e)||Se.test(e),parse:e=>pe.test(e)?pe.parse(e):Se.test(e)?Se.parse(e):me.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?pe.transform(e):Se.transform(e),getAnimatableNone:e=>{const t=Ae.parse(e);return t.alpha=0,Ae.transform(t)}},xe=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Ce="number",Oe="color",Re=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function _e(e){const t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[];let s=0;const a=t.replace(Re,(e=>(Ae.test(e)?(n.color.push(s),i.push(Oe),r.push(Ae.parse(e))):e.startsWith("var(")?(n.var.push(s),i.push("var"),r.push(e)):(n.number.push(s),i.push(Ce),r.push(parseFloat(e))),++s,"${}"))).split("${}");return{values:r,split:a,indexes:n,types:i}}function Pe(e){return _e(e).values}function ke(e){const{split:t,types:r}=_e(e),n=t.length;return e=>{let i="";for(let s=0;s<n;s++)if(i+=t[s],void 0!==e[s]){const t=r[s];i+=t===Ce?le(e[s]):t===Oe?Ae.transform(e[s]):e[s]}return i}}const De=e=>"number"==typeof e?0:Ae.test(e)?Ae.getAnimatableNone(e):e;const Ie={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(ce)?.length||0)+(e.match(xe)?.length||0)>0},parse:Pe,createTransformer:ke,getAnimatableNone:function(e){const t=Pe(e);return ke(e)(t.map(De))}};function Ne(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Me(e,t){return r=>r>0?t:e}const Le=(e,t,r)=>e+(t-e)*r,Fe=(e,t,r)=>{const n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},Ue=[me,pe,Se];function Be(e){const t=(r=e,Ue.find((e=>e.test(r))));var r;if(!Boolean(t))return!1;let n=t.parse(e);return t===Se&&(n=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,s=0,a=0;if(t/=100){const n=r<.5?r*(1+t):r+t-r*t,o=2*r-n;i=Ne(o,n,e+1/3),s=Ne(o,n,e),a=Ne(o,n,e-1/3)}else i=s=a=r;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(n)),n}const Ve=(e,t)=>{const r=Be(e),n=Be(t);if(!r||!n)return Me(e,t);const i={...r};return e=>(i.red=Fe(r.red,n.red,e),i.green=Fe(r.green,n.green,e),i.blue=Fe(r.blue,n.blue,e),i.alpha=Le(r.alpha,n.alpha,e),pe.transform(i))},We=new Set(["none","hidden"]);function je(e,t){return r=>Le(e,t,r)}function He(e){return"number"==typeof e?je:"string"==typeof e?ne(e)?Me:Ae.test(e)?Ve:ze:Array.isArray(e)?Ge:"object"==typeof e?Ae.test(e)?Ve:$e:Me}function Ge(e,t){const r=[...e],n=r.length,i=e.map(((e,r)=>He(e)(e,t[r])));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function $e(e,t){const r={...e,...t},n={};for(const i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=He(e[i])(e[i],t[i]));return e=>{for(const t in n)r[t]=n[t](e);return r}}const ze=(e,t)=>{const r=Ie.createTransformer(t),n=_e(e),i=_e(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?We.has(e)&&!i.values.length||We.has(t)&&!n.values.length?function(e,t){return We.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):E(Ge(function(e,t){const r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const s=t.types[i],a=e.indexes[s][n[s]],o=e.values[a]??0;r[i]=o,n[s]++}return r}(n,i),i.values),r):Me(e,t)};function Ye(e,t,r){if("number"==typeof e&&"number"==typeof t&&"number"==typeof r)return Le(e,t,r);return He(e)(e,t)}const Xe=e=>{const t=({timestamp:t})=>e(t);return{start:(e=!0)=>Y.update(t,e),stop:()=>X(t),now:()=>K.isProcessing?K.timestamp:Q.now()}},Ke=(e,t,r=10)=>{let n="";const i=Math.max(Math.round(t/r),2);for(let s=0;s<i;s++)n+=Math.round(1e4*e(s/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},Je=2e4;function qe(e){let t=0;let r=e.next(t);for(;!r.done&&t<Je;)t+=50,r=e.next(t);return t>=Je?1/0:t}function Ze(e,t,r){const n=Math.max(t-5,0);return C(r-e(n),t-n)}const Qe=100,et=10,tt=1,rt=0,nt=800,it=.3,st=.3,at={granular:.01,default:2},ot={granular:.005,default:.5},lt=.01,ct=10,ht=.05,ft=1,ut=.001;function dt({duration:e=nt,bounce:t=it,velocity:r=rt,mass:n=tt}){let i,s,a=1-t;a=d(ht,ft,a),e=d(lt,ct,x(e)),a<1?(i=t=>{const n=t*a,i=n*e,s=n-r,o=mt(t,a),l=Math.exp(-i);return ut-s/o*l},s=t=>{const n=t*a*e,s=n*r+r,o=Math.pow(a,2)*Math.pow(t,2)*e,l=Math.exp(-n),c=mt(Math.pow(t,2),a);return(-i(t)+ut>0?-1:1)*((s-o)*l)/c}):(i=t=>Math.exp(-t*e)*((t-r)*e+1)-.001,s=t=>Math.exp(-t*e)*(e*e*(r-t)));const o=function(e,t,r){let n=r;for(let i=1;i<pt;i++)n-=e(n)/t(n);return n}(i,s,5/e);if(e=A(e),isNaN(o))return{stiffness:Qe,damping:et,duration:e};{const t=Math.pow(o,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}const pt=12;function mt(e,t){return e*Math.sqrt(1-t*t)}const gt=["duration","bounce"],vt=["stiffness","damping","mass"];function Tt(e,t){return t.some((t=>void 0!==e[t]))}function yt(e=st,t=it){const r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:n,restDelta:i}=r;const s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],o={done:!1,value:s},{stiffness:l,damping:c,mass:h,duration:f,velocity:u,isResolvedFromDuration:p}=function(e){let t={velocity:rt,stiffness:Qe,damping:et,mass:tt,isResolvedFromDuration:!1,...e};if(!Tt(e,vt)&&Tt(e,gt))if(e.visualDuration){const r=e.visualDuration,n=2*Math.PI/(1.2*r),i=n*n,s=2*d(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:tt,stiffness:i,damping:s}}else{const r=dt(e);t={...t,...r,mass:tt},t.isResolvedFromDuration=!0}return t}({...r,velocity:-x(r.velocity||0)}),m=u||0,g=c/(2*Math.sqrt(l*h)),v=a-s,T=x(Math.sqrt(l/h)),y=Math.abs(v)<5;let w;if(n||(n=y?at.granular:at.default),i||(i=y?ot.granular:ot.default),g<1){const e=mt(T,g);w=t=>{const r=Math.exp(-g*T*t);return a-r*((m+g*T*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}}else if(1===g)w=e=>a-Math.exp(-T*e)*(v+(m+T*v)*e);else{const e=T*Math.sqrt(g*g-1);w=t=>{const r=Math.exp(-g*T*t),n=Math.min(e*t,300);return a-r*((m+g*T*v)*Math.sinh(n)+e*v*Math.cosh(n))/e}}const E={calculatedDuration:p&&f||null,next:e=>{const t=w(e);if(p)o.done=e>=f;else{let r=0===e?m:0;g<1&&(r=0===e?A(m):Ze(w,e,t));const s=Math.abs(r)<=n,l=Math.abs(a-t)<=i;o.done=s&&l}return o.value=o.done?a:t,o},toString:()=>{const e=Math.min(qe(E),Je),t=Ke((t=>E.next(e*t).value),e,30);return e+"ms "+t},toTransition:()=>{}};return E}function wt({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:c=.5,restSpeed:h}){const f=e[0],u={done:!1,value:f},d=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l;let p=r*t;const m=f+p,g=void 0===a?m:a(m);g!==m&&(p=g-f);const v=e=>-p*Math.exp(-e/n),T=e=>g+v(e),y=e=>{const t=v(e),r=T(e);u.done=Math.abs(t)<=c,u.value=u.done?g:r};let w,E;const b=e=>{var t;(t=u.value,void 0!==o&&t<o||void 0!==l&&t>l)&&(w=e,E=yt({keyframes:[u.value,d(u.value)],velocity:Ze(T,e,u.value),damping:i,stiffness:s,restDelta:c,restSpeed:h}))};return b(0),{calculatedDuration:null,next:e=>{let t=!1;return E||void 0!==w||(t=!0,y(e),b(e)),void 0!==w&&e>=w?E.next(e-w):(!t&&y(e),u)}}}function Et(e,t,{clamp:r=!0,ease:n,mixer:i}={}){const s=e.length;if(t.length,1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];const a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=function(e,t,r){const n=[],i=r||p.mix||Ye,s=e.length-1;for(let a=0;a<s;a++){let r=i(e[a],e[a+1]);if(t){const e=Array.isArray(t)?t[a]||y:t;r=E(e,r)}n.push(r)}return n}(t,n,i),l=o.length,c=r=>{if(a&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);const i=b(e[n],e[n+1],r);return o[n](i)};return r?t=>c(d(e[0],e[s-1],t)):c}function bt(e){const t=[0];return function(e,t){const r=e[e.length-1];for(let n=1;n<=t;n++){const i=b(0,t,n);e.push(Le(r,1,i))}}(t,e.length-1),t}function St({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){const i=(e=>Array.isArray(e)&&"number"!=typeof e[0])(n)?n.map(H):H(n),s={done:!1,value:t[0]},a=function(e,t){return e.map((e=>e*t))}(r&&r.length===t.length?r:bt(t),e),o=Et(a,t,{ease:Array.isArray(i)?i:(l=t,c=i,l.map((()=>c||V)).splice(0,l.length-1))});var l,c;return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}yt.applyToOptions=e=>{const t=function(e,t=100,r){const n=r({...e,keyframes:[0,t]}),i=Math.min(qe(n),Je);return{type:"keyframes",ease:e=>n.next(i*e).value/t,duration:x(i)}}(e,100,yt);return e.ease=t.ease,e.duration=A(t.duration),e.type="keyframes",e};const At=e=>null!==e;function xt(e,{repeat:t,repeatType:r="loop"},n,i=1){const s=e.filter(At),a=i<0||t&&"loop"!==r&&t%2==1?0:s.length-1;return a&&void 0!==n?n:s[a]}const Ct={decay:wt,inertia:wt,tween:St,keyframes:St,spring:yt};function Ot(e){"string"==typeof e.type&&(e.type=Ct[e.type])}class Rt{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((e=>{this.resolve=e}))}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}const _t=e=>e/100;class Pt extends Rt{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:e}=this.options;e&&e.updatedAt!==Q.now()&&this.tick(Q.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){const{options:e}=this;Ot(e);const{type:t=St,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:s=0}=e;let{keyframes:a}=e;const o=t||St;o!==St&&"number"!=typeof a[0]&&(this.mixKeyframes=E(_t,Ye(a[0],a[1])),a=[0,100]);const l=o({...e,keyframes:a});"mirror"===i&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=qe(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=l}updateTime(e){const t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){const{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return r.next(0);const{delay:l=0,keyframes:c,repeat:h,repeatType:f,repeatDelay:u,type:p,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);const v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),T=this.playbackSpeed>=0?v<0:v>n;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let y=this.currentTime,w=r;if(h){const e=Math.min(this.currentTime,n)/a;let t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,t=Math.min(t,h+1);Boolean(t%2)&&("reverse"===f?(r=1-r,u&&(r-=u/a)):"mirror"===f&&(w=s)),y=d(0,1,r)*a}const E=T?{done:!1,value:c[0]}:w.next(y);i&&(E.value=i(E.value));let{done:b}=E;T||null===o||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return S&&p!==wt&&(E.value=xt(c,this.options,g,this.speed)),m&&m(E.value),S&&this.finish(),E}then(e,t){return this.finished.then(e,t)}get duration(){return x(this.calculatedDuration)}get time(){return x(this.currentTime)}set time(e){e=A(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(Q.now());const t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=x(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=Xe,startTime:t}=this.options;this.driver||(this.driver=e((e=>this.tick(e)))),this.options.onPlay?.();const r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Q.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}const kt=e=>180*e/Math.PI,Dt=e=>{const t=kt(Math.atan2(e[1],e[0]));return Nt(t)},It={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Dt,rotateZ:Dt,skewX:e=>kt(Math.atan(e[1])),skewY:e=>kt(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Nt=e=>((e%=360)<0&&(e+=360),e),Mt=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),Lt=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Ft={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Mt,scaleY:Lt,scale:e=>(Mt(e)+Lt(e))/2,rotateX:e=>Nt(kt(Math.atan2(e[6],e[5]))),rotateY:e=>Nt(kt(Math.atan2(-e[2],e[0]))),rotateZ:Dt,rotate:Dt,skewX:e=>kt(Math.atan(e[4])),skewY:e=>kt(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Ut(e){return e.includes("scale")?1:0}function Bt(e,t){if(!e||"none"===e)return Ut(t);const r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,i;if(r)n=Ft,i=r;else{const t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=It,i=t}if(!i)return Ut(t);const s=n[t],a=i[1].split(",").map(Vt);return"function"==typeof s?s(a):a[s]}function Vt(e){return parseFloat(e.trim())}const Wt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],jt=(()=>new Set(Wt))(),Ht=e=>e===se||e===ye,Gt=new Set(["x","y","z"]),$t=Wt.filter((e=>!Gt.has(e)));const zt={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Bt(t,"x"),y:(e,{transform:t})=>Bt(t,"y")};zt.translateX=zt.x,zt.translateY=zt.y;const Yt=new Set;let Xt=!1,Kt=!1,Jt=!1;function qt(){if(Kt){const e=Array.from(Yt).filter((e=>e.needsMeasurement)),t=new Set(e.map((e=>e.element))),r=new Map;t.forEach((e=>{const t=function(e){const t=[];return $t.forEach((r=>{const n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))})),t}(e);t.length&&(r.set(e,t),e.render())})),e.forEach((e=>e.measureInitialState())),t.forEach((e=>{e.render();const t=r.get(e);t&&t.forEach((([t,r])=>{e.getValue(t)?.set(r)}))})),e.forEach((e=>e.measureEndState())),e.forEach((e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)}))}Kt=!1,Xt=!1,Yt.forEach((e=>e.complete(Jt))),Yt.clear()}function Zt(){Yt.forEach((e=>{e.readKeyframes(),e.needsMeasurement&&(Kt=!0)}))}class Qt{constructor(e,t,r,n,i,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(Yt.add(this),Xt||(Xt=!0,Y.read(Zt),Y.resolveKeyframes(qt))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){const i=n?.get(),s=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){const n=r.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===i&&n.set(e[0])}!function(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),Yt.delete(this)}cancel(){"scheduled"===this.state&&(Yt.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const er=T((()=>void 0!==window.ScrollTimeline)),tr={};function rr(e,t){const r=T(e);return()=>tr[t]??r()}const nr=rr((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0}),"linearEasing"),ir=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,sr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ir([0,.65,.55,1]),circOut:ir([.55,0,1,.45]),backIn:ir([.31,.01,.66,-.59]),backOut:ir([.33,1.53,.69,.99])};function ar(e,t){return e?"function"==typeof e?nr()?Ke(e,t):"ease-out":W(e)?ir(e):Array.isArray(e)?e.map((e=>ar(e,t)||sr.easeOut)):sr[e]:void 0}function or(e,t,r,{delay:n=0,duration:i=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},c=void 0){const h={[t]:r};l&&(h.offset=l);const f=ar(o,i);Array.isArray(f)&&(h.easing=f);const u={delay:n,duration:i,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};c&&(u.pseudoElement=c);return e.animate(h,u)}function lr(e){return"function"==typeof e&&"applyToOptions"in e}class cr extends Rt{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=Boolean(i),this.allowFlatten=s,this.options=e,e.type;const l=function({type:e,...t}){return lr(e)&&nr()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=or(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const e=xt(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){(e=>e.startsWith("--"))(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const e=this.animation.effect?.getComputedTiming?.().duration||0;return x(Number(e))}get time(){return x(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=A(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&er()?(this.animation.timeline=e,y):t(this)}}const hr={anticipate:N,backInOut:I,circInOut:F};function fr(e){"string"==typeof e.ease&&e.ease in hr&&(e.ease=hr[e.ease])}class ur extends cr{constructor(e){fr(e),Ot(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:t,onUpdate:r,onComplete:n,element:i,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);const a=new Pt({...s,autoplay:!1}),o=A(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}const dr=(e,t)=>"zIndex"!==t&&(!("number"!=typeof e&&!Array.isArray(e))||!("string"!=typeof e||!Ie.test(e)&&"0"!==e||e.startsWith("url(")));function pr(e){return g(e)&&"offsetHeight"in e}const mr=new Set(["opacity","clipPath","filter","transform"]),gr=T((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class vr extends Rt{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:c,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=Q.now();const f={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:s,name:o,motionValue:l,element:c,...h},u=c?.KeyframeResolver||Qt;this.keyframeResolver=new u(a,((e,t,r)=>this.onKeyframesResolved(e,t,f,!r)),o,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;const{name:i,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:c}=r;this.resolvedAt=Q.now(),function(e,t,r,n){const i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;const s=e[e.length-1],a=dr(i,t),o=dr(s,t);return!(!a||!o)&&(function(e){const t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||lr(r))&&n)}(e,i,s,a)||(!p.instantAnimations&&o||c?.(xt(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);const h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},f=!l&&function(e){const{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:s,type:a}=e;if(!pr(t?.owner?.current))return!1;const{onUpdate:o,transformTemplate:l}=t.owner.getProps();return gr()&&r&&mr.has(r)&&("transform"!==r||!l)&&!o&&!n&&"mirror"!==i&&0!==s&&"inertia"!==a}(h)?new ur({...h,element:h.motionValue.owner.current}):new Pt(h);f.finished.then((()=>this.notifyFinished())).catch(y),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),Jt=!0,Zt(),qt(),Jt=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Tr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function yr(e,t,r=1){const[n,i]=function(e){const t=Tr.exec(e);if(!t)return[,];const[,r,n,i]=t;return[`--${r??n}`,i]}(e);if(!n)return;const s=window.getComputedStyle(t).getPropertyValue(n);if(s){const e=s.trim();return m(e)?parseFloat(e):e}return ne(i)?yr(i,t,r+1):i}function wr(e,t){return e?.[t]??e?.default??e}const Er=new Set(["width","height","top","left","right","bottom",...Wt]),br=e=>t=>t.test(e),Sr=[se,ye,Te,ve,Ee,we,{test:e=>"auto"===e,parse:e=>e}],Ar=e=>Sr.find(br(e));const xr=new Set(["brightness","contrast","saturate","opacity"]);function Cr(e){const[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[n]=r.match(ce)||[];if(!n)return e;const i=r.replace(n,"");let s=xr.has(t)?1:0;return n!==r&&(s*=100),t+"("+s+i+")"}const Or=/\b([a-z-]*)\(.*?\)/gu,Rr={...Ie,getAnimatableNone:e=>{const t=e.match(Or);return t?t.map(Cr).join(" "):e}},_r={...se,transform:Math.round},Pr={borderWidth:ye,borderTopWidth:ye,borderRightWidth:ye,borderBottomWidth:ye,borderLeftWidth:ye,borderRadius:ye,radius:ye,borderTopLeftRadius:ye,borderTopRightRadius:ye,borderBottomRightRadius:ye,borderBottomLeftRadius:ye,width:ye,maxWidth:ye,height:ye,maxHeight:ye,top:ye,right:ye,bottom:ye,left:ye,padding:ye,paddingTop:ye,paddingRight:ye,paddingBottom:ye,paddingLeft:ye,margin:ye,marginTop:ye,marginRight:ye,marginBottom:ye,marginLeft:ye,backgroundPositionX:ye,backgroundPositionY:ye,...{rotate:ve,rotateX:ve,rotateY:ve,rotateZ:ve,scale:oe,scaleX:oe,scaleY:oe,scaleZ:oe,skew:ve,skewX:ve,skewY:ve,distance:ye,translateX:ye,translateY:ye,translateZ:ye,x:ye,y:ye,z:ye,perspective:ye,transformPerspective:ye,opacity:ae,originX:be,originY:be,originZ:ye},zIndex:_r,fillOpacity:ae,strokeOpacity:ae,numOctaves:_r},kr={...Pr,color:Ae,backgroundColor:Ae,outlineColor:Ae,fill:Ae,stroke:Ae,borderColor:Ae,borderTopColor:Ae,borderRightColor:Ae,borderBottomColor:Ae,borderLeftColor:Ae,filter:Rr,WebkitFilter:Rr},Dr=e=>kr[e];function Ir(e,t){let r=Dr(e);return r!==Rr&&(r=Ie),r.getAnimatableNone?r.getAnimatableNone(t):void 0}const Nr=new Set(["auto","none","0"]);class Mr extends Qt{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let o=0;o<e.length;o++){let r=e[o];if("string"==typeof r&&(r=r.trim(),ne(r))){const n=yr(r,t.current);void 0!==n&&(e[o]=n),o===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!Er.has(r)||2!==e.length)return;const[n,i]=e,s=Ar(n),a=Ar(i);if(s!==a)if(Ht(s)&&Ht(a))for(let o=0;o<e.length;o++){const t=e[o];"string"==typeof t&&(e[o]=parseFloat(t))}else zt[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:t}=this,r=[];for(let i=0;i<e.length;i++)(null===e[i]||("number"==typeof(n=e[i])?0===n:null===n||"none"===n||"0"===n||v(n)))&&r.push(i);var n;r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){const t=e[i];"string"==typeof t&&!Nr.has(t)&&_e(t).values.length&&(n=e[i]),i++}if(n&&r)for(const s of t)e[s]=Ir(r,n)}(e,r,t)}measureInitialState(){const{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=zt[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;const n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){const{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;const n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);const i=r.length-1,s=r[i];r[i]=zt[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach((([t,r])=>{e.getValue(t).set(r)})),this.resolveNoneKeyframes()}}const Lr=(e,t)=>t&&"number"==typeof e?t.transform(e):e;class Fr{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{const r=Q.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const n of this.dependents)n.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){var t;this.current=e,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=(t=this.current,!isNaN(parseFloat(t))))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new S);const r=this.events[e].add(t);return"change"===e?()=>{r(),Y.read((()=>{this.events.change.getSize()||this.stop()}))}:r}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;const t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return C(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise((t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ur(e,t){return new Fr(e,t)}const{schedule:Br}=z(queueMicrotask,!1),Vr={x:!1,y:!1};function Wr(){return Vr.x||Vr.y}function jr(e,t){const r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document;const n=r?.[e]??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function Hr(e){return!("touch"===e.pointerType||Wr())}const Gr=(e,t)=>!!t&&(e===t||Gr(e,t.parentElement)),$r=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,zr=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Yr=new WeakSet;function Xr(e){return t=>{"Enter"===t.key&&e(t)}}function Kr(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function Jr(e){return $r(e)&&!Wr()}function qr(e,t,r={}){const[n,i,s]=jr(e,r),a=e=>{const n=e.currentTarget;if(!Jr(e))return;Yr.add(n);const s=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),Yr.has(n)&&Yr.delete(n),Jr(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,n===window||n===document||r.useGlobalTarget||Gr(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return n.forEach((e=>{var t;(r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),pr(e)&&(e.addEventListener("focus",(e=>((e,t)=>{const r=e.currentTarget;if(!r)return;const n=Xr((()=>{if(Yr.has(r))return;Kr(r,"down");const e=Xr((()=>{Kr(r,"up")}));r.addEventListener("keyup",e,t),r.addEventListener("blur",(()=>Kr(r,"cancel")),t)}));r.addEventListener("keydown",n,t),r.addEventListener("blur",(()=>r.removeEventListener("keydown",n)),t)})(e,i))),t=e,zr.has(t.tagName)||-1!==t.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))})),s}function Zr(e){return g(e)&&"ownerSVGElement"in e}const Qr=e=>Boolean(e&&e.getVelocity),en=[...Sr,Ae,Ie],tn=e.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class rn extends e.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=t.offsetParent,r=pr(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function nn({children:t,isPresent:r,anchorX:n}){const i=e.useId(),a=e.useRef(null),o=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(tn);return e.useInsertionEffect((()=>{const{width:e,height:t,top:s,left:c,right:h}=o.current;if(r||!a.current||!e||!t)return;const f="left"===n?`left: ${c}`:`right: ${h}`;a.current.dataset.motionPopId=i;const u=document.createElement("style");return l&&(u.nonce=l),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${e}px !important;\n            height: ${t}px !important;\n            ${f}px !important;\n            top: ${s}px !important;\n          }\n        `),()=>{document.head.contains(u)&&document.head.removeChild(u)}}),[r]),s.jsx(rn,{isPresent:r,childRef:a,sizeRef:o,children:e.cloneElement(t,{ref:a})})}const sn=({children:t,initial:r,isPresent:n,onExitComplete:i,custom:a,presenceAffectsLayout:l,mode:c,anchorX:f})=>{const u=o(an),d=e.useId();let p=!0,m=e.useMemo((()=>(p=!1,{id:d,initial:r,isPresent:n,custom:a,onExitComplete:e=>{u.set(e,!0);for(const t of u.values())if(!t)return;i&&i()},register:e=>(u.set(e,!1),()=>u.delete(e))})),[n,u,i]);return l&&p&&(m={...m}),e.useMemo((()=>{u.forEach(((e,t)=>u.set(t,!1)))}),[n]),e.useEffect((()=>{!n&&!u.size&&i&&i()}),[n]),"popLayout"===c&&(t=s.jsx(nn,{isPresent:n,anchorX:f,children:t})),s.jsx(h.Provider,{value:m,children:t})};function an(){return new Map}function on(t=!0){const r=e.useContext(h);if(null===r)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=r,a=e.useId();e.useEffect((()=>{if(t)return s(a)}),[t]);const o=e.useCallback((()=>t&&i&&i(a)),[a,i,t]);return!n&&i?[!1,o]:[!0]}const ln=e=>e.key||"";function cn(t){const r=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&r.push(t)})),r}const hn=({children:t,custom:r,initial:n=!0,onExitComplete:i,presenceAffectsLayout:l=!0,mode:h="sync",propagate:f=!1,anchorX:u="left"})=>{const[d,p]=on(f),m=e.useMemo((()=>cn(t)),[t]),g=f&&!d?[]:m.map(ln),v=e.useRef(!0),T=e.useRef(m),y=o((()=>new Map)),[w,E]=e.useState(m),[b,S]=e.useState(m);c((()=>{v.current=!1,T.current=m;for(let e=0;e<b.length;e++){const t=ln(b[e]);g.includes(t)?y.delete(t):!0!==y.get(t)&&y.set(t,!1)}}),[b,g.length,g.join("-")]);const A=[];if(m!==w){let e=[...m];for(let t=0;t<b.length;t++){const r=b[t],n=ln(r);g.includes(n)||(e.splice(t,0,r),A.push(r))}return"wait"===h&&A.length&&(e=A),S(cn(e)),E(m),null}const{forceRender:x}=e.useContext(a);return s.jsx(s.Fragment,{children:b.map((e=>{const t=ln(e),a=!(f&&!d)&&(m===b||g.includes(t));return s.jsx(sn,{isPresent:a,initial:!(v.current&&!n)&&void 0,custom:r,presenceAffectsLayout:l,mode:h,onExitComplete:a?void 0:()=>{if(!y.has(t))return;y.set(t,!0);let e=!0;y.forEach((t=>{t||(e=!1)})),e&&(x?.(),S(T.current),f&&p?.(),i&&i())},anchorX:u,children:e},t)}))})},fn=e.createContext({strict:!1}),un={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},dn={};for(const $ in un)dn[$]={isEnabled:e=>un[$].some((t=>!!e[t]))};const pn=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function mn(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||pn.has(e)}let gn=e=>!mn(e);try{"function"==typeof(vn=require("@emotion/is-prop-valid").default)&&(gn=e=>e.startsWith("on")?!mn(e):vn(e))}catch{}var vn;function Tn(e){if("undefined"==typeof Proxy)return e;const t=new Map;return new Proxy(((...t)=>e(...t)),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}const yn=e.createContext({});function wn(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function En(e){return"string"==typeof e||Array.isArray(e)}const bn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Sn=["initial",...bn];function An(e){return wn(e.animate)||Sn.some((t=>En(e[t])))}function xn(e){return Boolean(An(e)||e.variants)}function Cn(t){const{initial:r,animate:n}=function(e,t){if(An(e)){const{initial:t,animate:r}=e;return{initial:!1===t||En(t)?t:void 0,animate:En(r)?r:void 0}}return!1!==e.inherit?t:{}}(t,e.useContext(yn));return e.useMemo((()=>({initial:r,animate:n})),[On(r),On(n)])}function On(e){return Array.isArray(e)?e.join(" "):e}const Rn=Symbol.for("motionComponentSymbol");function _n(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function Pn(t,r,n){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),r&&(e?r.mount(e):r.unmount()),n&&("function"==typeof n?n(e):_n(n)&&(n.current=e))}),[r])}const kn=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Dn="data-"+kn("framerAppearId"),In=e.createContext({});function Nn(t,r,n,i,s){const{visualElement:a}=e.useContext(yn),o=e.useContext(fn),l=e.useContext(h),f=e.useContext(tn).reducedMotion,u=e.useRef(null);i=i||o.renderer,!u.current&&i&&(u.current=i(t,{visualState:r,parent:a,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:f}));const d=u.current,p=e.useContext(In);!d||d.projection||!s||"html"!==d.type&&"svg"!==d.type||function(e,t,r,n){const{layoutId:i,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:c,layoutCrossfade:h}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:Mn(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:Boolean(a)||o&&_n(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:c})}(u.current,n,s,p);const m=e.useRef(!1);e.useInsertionEffect((()=>{d&&m.current&&d.update(n,l)}));const g=n[Dn],v=e.useRef(Boolean(g)&&!window.MotionHandoffIsComplete?.(g)&&window.MotionHasOptimisedAnimation?.(g));return c((()=>{d&&(m.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),Br.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())})),e.useEffect((()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(g)})),v.current=!1))})),d}function Mn(e){if(e)return!1!==e.options.allowProjection?e.projection:Mn(e.parent)}function Ln({preloadedFeatures:t,createVisualElement:r,useRender:n,useVisualState:i,Component:a}){function o(t,o){let c;const h={...e.useContext(tn),...t,layoutId:Fn(t)},{isStatic:f}=h,u=Cn(t),d=i(t,f);if(!f&&l){e.useContext(fn).strict;const t=function(e){const{drag:t,layout:r}=dn;if(!t&&!r)return{};const n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);c=t.MeasureLayout,u.visualElement=Nn(a,d,h,r,t.ProjectionNode)}return s.jsxs(yn.Provider,{value:u,children:[c&&u.visualElement?s.jsx(c,{visualElement:u.visualElement,...h}):null,n(a,t,Pn(d,u.visualElement,o),d,f,u.visualElement)]})}t&&function(e){for(const t in e)dn[t]={...dn[t],...e[t]}}(t),o.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;const c=e.forwardRef(o);return c[Rn]=a,c}function Fn({layoutId:t}){const r=e.useContext(a).id;return r&&void 0!==t?r+"-"+t:t}const Un={};function Bn(e,{layout:t,layoutId:r}){return jt.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!Un[e]||"opacity"===e)}const Vn={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Wn=Wt.length;function jn(e,t,r){const{style:n,vars:i,transformOrigin:s}=e;let a=!1,o=!1;for(const l in t){const e=t[l];if(jt.has(l))a=!0;else if(te(l))i[l]=e;else{const t=Lr(e,Pr[l]);l.startsWith("origin")?(o=!0,s[l]=t):n[l]=t}}if(t.transform||(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let s=0;s<Wn;s++){const a=Wt[s],o=e[a];if(void 0===o)continue;let l=!0;if(l="number"==typeof o?o===(a.startsWith("scale")?1:0):0===parseFloat(o),!l||r){const e=Lr(o,Pr[a]);l||(i=!1,n+=`${Vn[a]||a}(${e}) `),r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),o){const{originX:e="50%",originY:t="50%",originZ:r=0}=s;n.transformOrigin=`${e} ${t} ${r}`}}const Hn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Gn(e,t,r){for(const n in t)Qr(t[n])||Bn(n,r)||(e[n]=t[n])}function $n(t,r){const n={};return Gn(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},r){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return jn(e,r,t),Object.assign({},e.vars,e.style)}),[r])}(t,r)),n}function zn(e,t){const r={},n=$n(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":"pan-"+("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r}const Yn={offset:"stroke-dashoffset",array:"stroke-dasharray"},Xn={offset:"strokeDashoffset",array:"strokeDasharray"};function Kn(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:s=1,pathOffset:a=0,...o},l,c,h){if(jn(e,o,c),l)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:f,style:u}=e;f.transform&&(u.transform=f.transform,delete f.transform),(u.transform||f.transformOrigin)&&(u.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),u.transform&&(u.transformBox=h?.transformBox??"fill-box",delete f.transformBox),void 0!==t&&(f.x=t),void 0!==r&&(f.y=r),void 0!==n&&(f.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;const s=i?Yn:Xn;e[s.offset]=ye.transform(-n);const a=ye.transform(t),o=ye.transform(r);e[s.array]=`${a} ${o}`}(f,i,s,a,!1)}const Jn=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),qn=e=>"string"==typeof e&&"svg"===e.toLowerCase();function Zn(t,r,n,i){const s=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Kn(e,r,qn(i),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}}),[r]);if(t.style){const e={};Gn(e,t.style,t),s.style={...e,...s.style}}return s}const Qn=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ei(e){return"string"==typeof e&&!e.includes("-")&&!!(Qn.indexOf(e)>-1||/[A-Z]/u.test(e))}function ti(t=!1){return(r,n,i,{latestValues:s},a)=>{const o=(ei(r)?Zn:zn)(n,s,a,r),l=function(e,t,r){const n={};for(const i in e)"values"===i&&"object"==typeof e.values||(gn(i)||!0===r&&mn(i)||!t&&!mn(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(n,"string"==typeof r,t),c=r!==e.Fragment?{...l,...o,ref:i}:{},{children:h}=n,f=e.useMemo((()=>Qr(h)?h.get():h),[h]);return e.createElement(r,{...c,children:f})}}function ri(e){const t=[{},{}];return e?.values.forEach(((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()})),t}function ni(e,t,r,n){if("function"==typeof t){const[i,s]=ri(n);t=t(void 0!==r?r:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){const[i,s]=ri(n);t=t(void 0!==r?r:e.custom,i,s)}return t}function ii(e){return Qr(e)?e.get():e}const si=t=>(r,n)=>{const i=e.useContext(yn),s=e.useContext(h),a=()=>function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,i){return{latestValues:ai(r,n,i,e),renderState:t()}}(t,r,i,s);return n?a():o(a)};function ai(e,t,r,n){const i={},s=n(e,{});for(const u in s)i[u]=ii(s[u]);let{initial:a,animate:o}=e;const l=An(e),c=xn(e);t&&c&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let h=!!r&&!1===r.initial;h=h||!1===a;const f=h?o:a;if(f&&"boolean"!=typeof f&&!wn(f)){const t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){const n=ni(e,t[r]);if(n){const{transitionEnd:e,transition:t,...r}=n;for(const n in r){let e=r[n];if(Array.isArray(e)){e=e[h?e.length-1:0]}null!==e&&(i[n]=e)}for(const n in e)i[n]=e[n]}}}return i}function oi(e,t,r){const{style:n}=e,i={};for(const s in n)(Qr(n[s])||t.style&&Qr(t.style[s])||Bn(s,e)||void 0!==r?.getValue(s)?.liveStyle)&&(i[s]=n[s]);return i}const li={useVisualState:si({scrapeMotionValuesFromProps:oi,createRenderState:Hn})};function ci(e,t,r){const n=oi(e,t,r);for(const i in e)if(Qr(e[i])||Qr(t[i])){n[-1!==Wt.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]}return n}const hi={useVisualState:si({scrapeMotionValuesFromProps:ci,createRenderState:Jn})};function fi(e,t){return function(r,{forwardMotionProps:n}={forwardMotionProps:!1}){return Ln({...ei(r)?hi:li,preloadedFeatures:e,useRender:ti(n),createVisualElement:t,Component:r})}}function ui(e,t,r){const n=e.getProps();return ni(n,t,void 0!==r?r:n.custom,e)}const di=e=>Array.isArray(e);function pi(e,t,r){e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,Ur(r))}function mi(e,t){const r=ui(e,t);let{transitionEnd:n={},transition:i={},...s}=r||{};s={...s,...n};for(const o in s){pi(e,o,(a=s[o],di(a)?a[a.length-1]||0:a))}var a}function gi(e,t){const r=e.getValue("willChange");if(n=r,Boolean(Qr(n)&&n.add))return r.add(t);if(!r&&p.WillChange){const r=new p.WillChange("auto");e.addValue("willChange",r),r.add(t)}var n}function vi(e){return e.props[Dn]}const Ti=e=>null!==e;const yi={type:"spring",stiffness:500,damping:25,restSpeed:10},wi={type:"keyframes",duration:.8},Ei={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},bi=(e,{keyframes:t})=>t.length>2?wi:jt.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:yi:Ei;const Si=(e,t,r,n={},i,s)=>a=>{const o=wr(n,e)||{},l=o.delay||n.delay||0;let{elapsed:c=0}=n;c-=A(l);const h={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-c,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:i};(function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:c,...h}){return!!Object.keys(h).length})(o)||Object.assign(h,bi(e,h)),h.duration&&(h.duration=A(h.duration)),h.repeatDelay&&(h.repeatDelay=A(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(f=!0)),(p.instantAnimations||p.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,f&&!s&&void 0!==t.get()){const e=function(e,{repeat:t,repeatType:r="loop"}){const n=e.filter(Ti);return n[t&&"loop"!==r&&t%2==1?0:n.length-1]}(h.keyframes,o);if(void 0!==e)return void Y.update((()=>{h.onUpdate(e),h.onComplete()}))}return o.isSync?new Pt(h):new vr(h)};function Ai({protectedKeys:e,needsAnimating:t},r){const n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}function xi(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;n&&(s=n);const l=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const h in o){const t=e.getValue(h,e.latestValues[h]??null),n=o[h];if(void 0===n||c&&Ai(c,h))continue;const i={delay:r,...wr(s||{},h)},a=t.get();if(void 0!==a&&!t.isAnimating&&!Array.isArray(n)&&n===a&&!i.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){const t=vi(e);if(t){const e=window.MotionHandoffAnimation(t,h,Y);null!==e&&(i.startTime=e,f=!0)}}gi(e,h),t.start(Si(h,t,n,e.shouldReduceMotion&&Er.has(h)?{type:!1}:i,e,f));const u=t.animation;u&&l.push(u)}return a&&Promise.all(l).then((()=>{Y.update((()=>{a&&mi(e,a)}))})),l}function Ci(e,t,r={}){const n=ui(e,t,"exit"===r.type?e.presenceContext?.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);const s=n?()=>Promise.all(xi(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{const{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=i;return function(e,t,r=0,n=0,i=1,s){const a=[],o=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>o-e*n;return Array.from(e.variantChildren).sort(Oi).forEach(((e,n)=>{e.notify("AnimationStart",t),a.push(Ci(e,t,{...s,delay:r+l(n)}).then((()=>e.notify("AnimationComplete",t))))})),Promise.all(a)}(e,t,s+n,a,o,r)}:()=>Promise.resolve(),{when:o}=i;if(o){const[e,t]="beforeChildren"===o?[s,a]:[a,s];return e().then((()=>t()))}return Promise.all([s(),a(r.delay)])}function Oi(e,t){return e.sortNodePosition(t)}function Ri(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map((t=>Ci(e,t,r)));n=Promise.all(i)}else if("string"==typeof t)n=Ci(e,t,r);else{const i="function"==typeof t?ui(e,t,r.custom):t;n=Promise.all(xi(e,i,r))}return n.then((()=>{e.notify("AnimationComplete",t)}))}function _i(e,t){if(!Array.isArray(t))return!1;const r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}const Pi=Sn.length;function ki(e){if(!e)return;if(!e.isControllingVariants){const t=e.parent&&ki(e.parent)||{};return void 0!==e.props.initial&&(t.initial=e.props.initial),t}const t={};for(let r=0;r<Pi;r++){const n=Sn[r],i=e.props[n];(En(i)||!1===i)&&(t[n]=i)}return t}const Di=[...bn].reverse(),Ii=bn.length;function Ni(e){let t=function(e){return t=>Promise.all(t.map((({animation:t,options:r})=>Ri(e,t,r))))}(e),r=Fi(),n=!0;const i=t=>(r,n)=>{const i=ui(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){const{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function s(s){const{props:a}=e,o=ki(e.parent)||{},l=[],c=new Set;let h={},f=1/0;for(let t=0;t<Ii;t++){const u=Di[t],d=r[u],p=void 0!==a[u]?a[u]:o[u],m=En(p),g=u===s?d.isActive:null;!1===g&&(f=t);let v=p===o[u]&&p!==a[u]&&m;if(v&&n&&e.manuallyAnimateOnMount&&(v=!1),d.protectedKeys={...h},!d.isActive&&null===g||!p&&!d.prevProp||wn(p)||"boolean"==typeof p)continue;const T=Mi(d.prevProp,p);let y=T||u===s&&d.isActive&&!v&&m||t>f&&m,w=!1;const E=Array.isArray(p)?p:[p];let b=E.reduce(i(u),{});!1===g&&(b={});const{prevResolvedValues:S={}}=d,A={...S,...b},x=t=>{y=!0,c.has(t)&&(w=!0,c.delete(t)),d.needsAnimating[t]=!0;const r=e.getValue(t);r&&(r.liveStyle=!1)};for(const e in A){const t=b[e],r=S[e];if(h.hasOwnProperty(e))continue;let n=!1;n=di(t)&&di(r)?!_i(t,r):t!==r,n?null!=t?x(e):c.add(e):void 0!==t&&c.has(e)?x(e):d.protectedKeys[e]=!0}d.prevProp=p,d.prevResolvedValues=b,d.isActive&&(h={...h,...b}),n&&e.blockInitialAnimation&&(y=!1);y&&(!(v&&T)||w)&&l.push(...E.map((e=>({animation:e,options:{type:u}}))))}if(c.size){const t={};if("boolean"!=typeof a.initial){const r=ui(e,Array.isArray(a.initial)?a.initial[0]:a.initial);r&&r.transition&&(t.transition=r.transition)}c.forEach((r=>{const n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null})),l.push({animation:t})}let u=Boolean(l.length);return!n||!1!==a.initial&&a.initial!==a.animate||e.manuallyAnimateOnMount||(u=!1),n=!1,u?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach((e=>e.animationState?.setActive(t,n))),r[t].isActive=n;const i=s(t);for(const e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=Fi(),n=!0}}}function Mi(e,t){return"string"==typeof t?t!==e:!!Array.isArray(t)&&!_i(t,e)}function Li(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Fi(){return{animate:Li(!0),whileInView:Li(),whileHover:Li(),whileTap:Li(),whileDrag:Li(),whileFocus:Li(),exit:Li()}}class Ui{constructor(e){this.isMounted=!1,this.node=e}update(){}}let Bi=0;const Vi={animation:{Feature:class extends Ui{constructor(e){super(e),e.animationState||(e.animationState=Ni(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();wn(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Ui{constructor(){super(...arguments),this.id=Bi++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then((()=>{t(this.id)}))}mount(){const{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}}};function Wi(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function ji(e){return{point:{x:e.pageX,y:e.pageY}}}function Hi(e,t,r,n){return Wi(e,t,(e=>t=>$r(t)&&e(t,ji(t)))(r),n)}function Gi({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function $i(e){return e.max-e.min}function zi(e,t,r,n=.5){e.origin=n,e.originPoint=Le(t.min,t.max,e.origin),e.scale=$i(r)/$i(t),e.translate=Le(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function Yi(e,t,r,n){zi(e.x,t.x,r.x,n?n.originX:void 0),zi(e.y,t.y,r.y,n?n.originY:void 0)}function Xi(e,t,r){e.min=r.min+t.min,e.max=e.min+$i(t)}function Ki(e,t,r){e.min=t.min-r.min,e.max=e.min+$i(t)}function Ji(e,t,r){Ki(e.x,t.x,r.x),Ki(e.y,t.y,r.y)}const qi=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Zi(e){return[e("x"),e("y")]}function Qi(e){return void 0===e||1===e}function es({scale:e,scaleX:t,scaleY:r}){return!Qi(e)||!Qi(t)||!Qi(r)}function ts(e){return es(e)||rs(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rs(e){return ns(e.x)||ns(e.y)}function ns(e){return e&&"0%"!==e}function is(e,t,r){return r+t*(e-r)}function ss(e,t,r,n,i){return void 0!==i&&(e=is(e,i,n)),is(e,r,n)+t}function as(e,t=0,r=1,n,i){e.min=ss(e.min,t,r,n,i),e.max=ss(e.max,t,r,n,i)}function os(e,{x:t,y:r}){as(e.x,t.translate,t.scale,t.originPoint),as(e.y,r.translate,r.scale,r.originPoint)}const ls=.999999999999,cs=1.0000000000001;function hs(e,t){e.min=e.min+t,e.max=e.max+t}function fs(e,t,r,n,i=.5){as(e,t,r,Le(e.min,e.max,i),n)}function us(e,t){fs(e.x,t.x,t.scaleX,t.scale,t.originX),fs(e.y,t.y,t.scaleY,t.scale,t.originY)}function ds(e,t){return Gi(function(e,t){if(!t)return e;const r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}const ps=({current:e})=>e?e.ownerDocument.defaultView:null,ms=(e,t)=>Math.abs(e-t);class gs{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=ys(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){const r=ms(e.x,t.x),n=ms(e.y,t.y);return Math.sqrt(r**2+n**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;const{point:n}=e,{timestamp:i}=K;this.history.push({...n,timestamp:i});const{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=vs(t,this.transformPagePoint),Y.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const s=ys("pointercancel"===e.type?this.lastMoveEventInfo:vs(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),n&&n(e,s)},!$r(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;const s=vs(ji(e),this.transformPagePoint),{point:a}=s,{timestamp:o}=K;this.history=[{...a,timestamp:o}];const{onSessionStart:l}=t;l&&l(e,ys(s,this.history)),this.removeListeners=E(Hi(this.contextWindow,"pointermove",this.handlePointerMove),Hi(this.contextWindow,"pointerup",this.handlePointerUp),Hi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function vs(e,t){return t?{point:t(e.point)}:e}function Ts(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ys({point:e},t){return{point:e,delta:Ts(e,Es(t)),offset:Ts(e,ws(t)),velocity:bs(t,.1)}}function ws(e){return e[0]}function Es(e){return e[e.length-1]}function bs(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null;const i=Es(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>A(t)));)r--;if(!n)return{x:0,y:0};const s=x(i.timestamp-n.timestamp);if(0===s)return{x:0,y:0};const a={x:(i.x-n.x)/s,y:(i.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function Ss(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function As(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}const xs=.35;function Cs(e,t,r){return{min:Os(e,t),max:Os(e,r)}}function Os(e,t){return"number"==typeof e?e:e[t]||0}const Rs=new WeakMap;class _s{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e,{snapToCursor:t=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;const{dragSnapToOrigin:n}=this.getProps();this.panSession=new gs(e,{onSessionStart:e=>{const{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ji(e).point)},onStart:(e,t)=>{const{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(s=r)||"y"===s?Vr[s]?null:(Vr[s]=!0,()=>{Vr[s]=!1}):Vr.x||Vr.y?null:(Vr.x=Vr.y=!0,()=>{Vr.x=Vr.y=!1}),!this.openDragLock))return;var s;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Zi((e=>{let t=this.getAxisMotionValue(e).get()||0;if(Te.test(t)){const{projection:r}=this.visualElement;if(r&&r.layout){const n=r.layout.layoutBox[e];if(n){t=$i(n)*(parseFloat(t)/100)}}}this.originPoint[e]=t})),i&&Y.postRender((()=>i(e,t))),gi(this.visualElement,"transform");const{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;const{offset:a}=t;if(n&&null===this.currentDirection)return this.currentDirection=function(e,t=10){let r=null;Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x");return r}(a),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>Zi((e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ps(this.visualElement)})}stop(e,t){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:n}=t;this.startAnimation(n);const{onDragEnd:i}=this.getProps();i&&Y.postRender((()=>i(e,t)))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){const{drag:n}=this.getProps();if(!r||!Ps(e,n,this.currentDirection))return;const i=this.getAxisMotionValue(e);let s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?Le(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?Le(r,e,n.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),i.set(s)}resolveConstraints(){const{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&_n(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!r)&&function(e,{top:t,left:r,bottom:n,right:i}){return{x:Ss(e.x,r,i),y:Ss(e.y,t,n)}}(r.layoutBox,e),this.elastic=function(e=xs){return!1===e?e=0:!0===e&&(e=xs),{x:Cs(e,"left","right"),y:Cs(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Zi((e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))}))}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!_n(e))return!1;const r=e.current,{projection:n}=this.visualElement;if(!n||!n.layout)return!1;const i=function(e,t,r){const n=ds(e,r),{scroll:i}=t;return i&&(hs(n.x,i.offset.x),hs(n.y,i.offset.y)),n}(r,n.root,this.visualElement.getTransformPagePoint());let s=function(e,t){return{x:As(e.x,t.x),y:As(e.y,t.y)}}(n.layout.layoutBox,i);if(t){const e=t(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=Gi(e))}return s}startAnimation(e){const{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{},l=Zi((a=>{if(!Ps(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});const c=n?200:1e6,h=n?40:1e7,f={type:"inertia",velocity:r?e[a]:0,bounceStiffness:c,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,f)}));return Promise.all(l).then(a)}startAxisValueAnimation(e,t){const r=this.getAxisMotionValue(e);return gi(this.visualElement,e),r.start(Si(e,r,0,t,this.visualElement,!1))}stopAnimation(){Zi((e=>this.getAxisMotionValue(e).stop()))}pauseAnimation(){Zi((e=>this.getAxisMotionValue(e).animation?.pause()))}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){const t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps(),n=r[t];return n||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){Zi((t=>{const{drag:r}=this.getProps();if(!Ps(t,r,this.currentDirection))return;const{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){const{min:r,max:s}=n.layout.layoutBox[t];i.set(e[t]-Le(r,s,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!_n(t)||!r||!this.constraints)return;this.stopAnimation();const n={x:0,y:0};Zi((e=>{const t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){const r=t.get();n[e]=function(e,t){let r=.5;const n=$i(e),i=$i(t);return i>n?r=b(t.min,t.max-n,e.min):n>i&&(r=b(e.min,e.max-i,t.min)),d(0,1,r)}({min:r,max:r},this.constraints[e])}}));const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Zi((t=>{if(!Ps(t,e,null))return;const r=this.getAxisMotionValue(t),{min:i,max:s}=this.constraints[t];r.set(Le(i,s,n[t]))}))}addListeners(){if(!this.visualElement.current)return;Rs.set(this.visualElement,this);const e=Hi(this.visualElement.current,"pointerdown",(e=>{const{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)})),t=()=>{const{dragConstraints:e}=this.getProps();_n(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),Y.read(t);const i=Wi(window,"resize",(()=>this.scalePositionWithinConstraints())),s=r.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(Zi((t=>{const r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))})),this.visualElement.render())}));return()=>{i(),e(),n(),s&&s()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:s=xs,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function Ps(e,t,r){return!(!0!==t&&t!==e||null!==r&&r!==e)}const ks=e=>(t,r)=>{e&&Y.postRender((()=>e(t,r)))};const Ds={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Is(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ns={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!ye.test(e))return e;e=parseFloat(e)}return`${Is(e,t.target.x)}% ${Is(e,t.target.y)}%`}},Ms={correct:(e,{treeScale:t,projectionDelta:r})=>{const n=e,i=Ie.parse(e);if(i.length>5)return n;const s=Ie.createTransformer(e),a="number"!=typeof i[0]?1:0,o=r.x.scale*t.x,l=r.y.scale*t.y;i[0+a]/=o,i[1+a]/=l;const c=Le(o,l,.5);return"number"==typeof i[2+a]&&(i[2+a]/=c),"number"==typeof i[3+a]&&(i[3+a]/=c),s(i)}};class Ls extends e.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;!function(e){for(const t in e)Un[t]=e[t],te(t)&&(Un[t].isCSSVariable=!0)}(Us),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",(()=>{this.safeToRemove()})),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Ds.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:s}=r;return s?(s.isPresent=i,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?s.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?s.promote():s.relegate()||Y.postRender((()=>{const e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Br.postRender((()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Fs(t){const[r,n]=on(),i=e.useContext(a);return s.jsx(Ls,{...t,layoutGroup:i,switchLayoutGroup:e.useContext(In),isPresent:r,safeToRemove:n})}const Us={borderRadius:{...Ns,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ns,borderTopRightRadius:Ns,borderBottomLeftRadius:Ns,borderBottomRightRadius:Ns,boxShadow:Ms};const Bs=(e,t)=>e.depth-t.depth;class Vs{constructor(){this.children=[],this.isDirty=!1}add(e){f(this.children,e),this.isDirty=!0}remove(e){u(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Bs),this.isDirty=!1,this.children.forEach(e)}}const Ws=["TopLeft","TopRight","BottomLeft","BottomRight"],js=Ws.length,Hs=e=>"string"==typeof e?parseFloat(e):e,Gs=e=>"number"==typeof e||ye.test(e);function $s(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const zs=Xs(0,.5,L),Ys=Xs(.5,.95,y);function Xs(e,t,r){return n=>n<e?0:n>t?1:r(b(e,t,n))}function Ks(e,t){e.min=t.min,e.max=t.max}function Js(e,t){Ks(e.x,t.x),Ks(e.y,t.y)}function qs(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Zs(e,t,r,n,i){return e=is(e-=t,1/r,n),void 0!==i&&(e=is(e,1/i,n)),e}function Qs(e,t,[r,n,i],s,a){!function(e,t=0,r=1,n=.5,i,s=e,a=e){Te.test(t)&&(t=parseFloat(t),t=Le(a.min,a.max,t/100)-a.min);if("number"!=typeof t)return;let o=Le(s.min,s.max,n);e===s&&(o-=t),e.min=Zs(e.min,t,r,o,i),e.max=Zs(e.max,t,r,o,i)}(e,t[r],t[n],t[i],t.scale,s,a)}const ea=["x","scaleX","originX"],ta=["y","scaleY","originY"];function ra(e,t,r,n){Qs(e.x,t,ea,r?r.x:void 0,n?n.x:void 0),Qs(e.y,t,ta,r?r.y:void 0,n?n.y:void 0)}function na(e){return 0===e.translate&&1===e.scale}function ia(e){return na(e.x)&&na(e.y)}function sa(e,t){return e.min===t.min&&e.max===t.max}function aa(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function oa(e,t){return aa(e.x,t.x)&&aa(e.y,t.y)}function la(e){return $i(e.x)/$i(e.y)}function ca(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class ha{constructor(){this.members=[]}add(e){f(this.members,e),e.scheduleRender()}remove(e){if(u(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex((t=>e===t));if(0===t)return!1;let r;for(let n=t;n>=0;n--){const e=this.members[n];if(!1!==e.isPresent){r=e;break}}return!!r&&(this.promote(r),!0)}promote(e,t){const r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach((e=>{const{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()}))}scheduleRender(){this.members.forEach((e=>{e.instance&&e.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const fa=["","X","Y","Z"],ua={visibility:"hidden"};let da=0;function pa(e,t,r,n){const{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function ma(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const r=vi(t);if(window.MotionHasOptimisedAnimation(r,"transform")){const{layout:t,layoutId:n}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",Y,!(t||n))}const{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&ma(n)}function ga({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=da++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(ya),this.nodes.forEach(Ca),this.nodes.forEach(Oa),this.nodes.forEach(wa)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Vs)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new S),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){const r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;var r;this.isSVG=Zr(t)&&!(Zr(r=t)&&"svg"===r.tagName),this.instance=t;const{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),e){let r;const n=()=>this.root.updateBlockedByResize=!1;e(t,(()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){const r=Q.now(),n=({timestamp:i})=>{const s=i-r;s>=t&&(X(n),e(s-t))};return Y.setup(n,!0),()=>X(n)}(n,250),Ds.hasAnimatedSinceResize&&(Ds.hasAnimatedSinceResize=!1,this.nodes.forEach(xa))}))}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const i=this.options.transition||s.getDefaultTransition()||Ia,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!oa(this.targetLayout,n),c=!t&&r;if(this.options.layoutRoot||this.resumeFrom||c||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const t={...wr(i,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||xa(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ra),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ma(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;const n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(ba);this.isUpdating||this.nodes.forEach(Sa),this.isUpdating=!1,this.nodes.forEach(Aa),this.nodes.forEach(va),this.nodes.forEach(Ta),this.clearAllSnapshots();const e=Q.now();K.delta=d(0,1e3/60,e-K.timestamp),K.timestamp=e,K.isProcessing=!0,J.update.process(K),J.preRender.process(K),J.render.process(K),K.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Br.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Ea),this.sharedNodes.forEach(_a)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Y.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||$i(this.snapshot.measuredBox.x)||$i(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let r=0;r<this.path.length;r++){this.path[r].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){const t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;const e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ia(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||ts(this.latestValues)||s)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){const t=this.measurePageBox();let r=this.removeElementScroll(t);var n;return e&&(r=this.removeTransform(r)),La((n=r).x),La(n.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Ua))){const{scroll:e}=this.root;e&&(hs(t.x,e.offset.x),hs(t.y,e.offset.y))}return t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};if(Js(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){const n=this.path[r],{scroll:i,options:s}=n;n!==this.root&&i&&s.layoutScroll&&(i.wasRoot&&Js(t,e),hs(t.x,i.offset.x),hs(t.y,i.offset.y))}return t}applyTransform(e,t=!1){const r={x:{min:0,max:0},y:{min:0,max:0}};Js(r,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&us(r,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),ts(e.latestValues)&&us(r,e.latestValues)}return ts(this.latestValues)&&us(r,this.latestValues),r}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Js(t,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];if(!e.instance)continue;if(!ts(e.latestValues))continue;es(e.latestValues)&&e.updateSnapshot();const n={x:{min:0,max:0},y:{min:0,max:0}};Js(n,e.measurePageBox()),ra(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,n)}return ts(this.latestValues)&&ra(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==K.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){const t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=K.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ji(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),Js(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var s,a,o;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,Xi(s.x,a.x,o.x),Xi(s.y,a.y,o.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Js(this.target,this.layout.layoutBox),os(this.target,this.targetDelta)):Js(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ji(this.relativeTargetOrigin,this.target,e.target),Js(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!es(this.parent.latestValues)&&!rs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const e=this.getLead(),t=Boolean(this.resumingFrom)||this!==e;let r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===K.timestamp&&(r=!1),r)return;const{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!n&&!i)return;Js(this.layoutCorrected,this.layout.layoutBox);const s=this.treeScale.x,a=this.treeScale.y;!function(e,t,r,n=!1){const i=r.length;if(!i)return;let s,a;t.x=t.y=1;for(let o=0;o<i;o++){s=r[o],a=s.projectionDelta;const{visualElement:i}=s.options;i&&i.props.style&&"contents"===i.props.style.display||(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&us(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,os(e,a)),n&&ts(s.latestValues)&&us(e,s.latestValues))}t.x<cs&&t.x>ls&&(t.x=1),t.y<cs&&t.y>ls&&(t.y=1)}(this.layoutCorrected,this.treeScale,this.path,t),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:o}=e;o?(this.projectionDelta&&this.prevProjectionDelta?(qs(this.prevProjectionDelta.x,this.projectionDelta.x),qs(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Yi(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&ca(this.projectionDelta.x,this.prevProjectionDelta.x)&&ca(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(e,t=!1){const r=this.snapshot,n=r?r.latestValues:{},i={...this.latestValues},s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const a={x:{min:0,max:0},y:{min:0,max:0}},o=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,h=Boolean(o&&!c&&!0===this.options.crossfade&&!this.path.some(Da));let f;this.animationProgress=0,this.mixTargetDelta=t=>{const r=t/1e3;var l,u,d,p,m,g;Pa(s.x,e.x,r),Pa(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ji(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=r,ka(d.x,p.x,m.x,g),ka(d.y,p.y,m.y,g),f&&(l=this.relativeTarget,u=f,sa(l.x,u.x)&&sa(l.y,u.y))&&(this.isProjectionDirty=!1),f||(f={x:{min:0,max:0},y:{min:0,max:0}}),Js(f,this.relativeTarget)),o&&(this.animationValues=i,function(e,t,r,n,i,s){i?(e.opacity=Le(0,r.opacity??1,zs(n)),e.opacityExit=Le(t.opacity??1,0,Ys(n))):s&&(e.opacity=Le(t.opacity??1,r.opacity??1,n));for(let a=0;a<js;a++){const i=`border${Ws[a]}Radius`;let s=$s(t,i),o=$s(r,i);void 0===s&&void 0===o||(s||(s=0),o||(o=0),0===s||0===o||Gs(s)===Gs(o)?(e[i]=Math.max(Le(Hs(s),Hs(o),n),0),(Te.test(o)||Te.test(s))&&(e[i]+="%")):e[i]=o)}(t.rotate||r.rotate)&&(e.rotate=Le(t.rotate||0,r.rotate||0,n))}(i,n,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Y.update((()=>{Ds.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ur(0)),this.currentAnimation=function(e,t,r){const n=Qr(e)?e:Ur(e);return n.start(Si("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&Fa(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=$i(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;const n=$i(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}Js(t,r),us(t,i),Yi(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new ha);this.sharedNodes.get(e).add(t);const r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){const{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){const n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;const n={};r.z&&pa("z",e,n,this.animationValues);for(let i=0;i<fa.length;i++)pa(`rotate${fa[i]}`,e,n,this.animationValues),pa(`skew${fa[i]}`,e,n,this.animationValues);e.render();for(const i in n)e.setStaticValue(i,n[i]),this.animationValues&&(this.animationValues[i]=n[i]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ua;const t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=ii(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;const n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ii(e?.pointerEvents)||""),this.hasProjected&&!ts(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}const i=n.animationValues||n.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let n="";const i=e.x.translate/t.x,s=e.y.translate/t.y,a=r?.z||0;if((i||s||a)&&(n=`translate3d(${i}px, ${s}px, ${a}px) `),1===t.x&&1===t.y||(n+=`scale(${1/t.x}, ${1/t.y}) `),r){const{transformPerspective:e,rotate:t,rotateX:i,rotateY:s,skewX:a,skewY:o}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}const o=e.x.scale*t.x,l=e.y.scale*t.y;return 1===o&&1===l||(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),r&&(t.transform=r(i,t.transform));const{x:s,y:a}=this.projectionDelta;t.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(const o in Un){if(void 0===i[o])continue;const{correct:e,applyTo:r,isCSSVariable:s}=Un[o],a="none"===t.transform?i[o]:e(i[o],n);if(r){const e=r.length;for(let n=0;n<e;n++)t[r[n]]=a}else s?this.options.visualElement.renderState.vars[o]=a:t[o]=a}return this.options.layoutId&&(t.pointerEvents=n===this?ii(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((e=>e.currentAnimation?.stop())),this.root.nodes.forEach(ba),this.root.sharedNodes.clear()}}}function va(e){e.updateLayout()}function Ta(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,s=t.source!==e.layout.source;"size"===i?Zi((e=>{const n=s?t.measuredBox[e]:t.layoutBox[e],i=$i(n);n.min=r[e].min,n.max=n.min+i})):Fa(i,t.layoutBox,r)&&Zi((n=>{const i=s?t.measuredBox[n]:t.layoutBox[n],a=$i(r[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)}));const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Yi(a,r,t.layoutBox);const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};s?Yi(o,e.applyTransform(n,!0),t.measuredBox):Yi(o,r,t.layoutBox);const l=!ia(a);let c=!1;if(!e.resumeFrom){const n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){const{snapshot:i,layout:s}=n;if(i&&s){const a={x:{min:0,max:0},y:{min:0,max:0}};Ji(a,t.layoutBox,i.layoutBox);const o={x:{min:0,max:0},y:{min:0,max:0}};Ji(o,r,s.layoutBox),oa(a,o)||(c=!0),n.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function ya(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function wa(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Ea(e){e.clearSnapshot()}function ba(e){e.clearMeasurements()}function Sa(e){e.isLayoutDirty=!1}function Aa(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function xa(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ca(e){e.resolveTargetDelta()}function Oa(e){e.calcProjection()}function Ra(e){e.resetSkewAndRotation()}function _a(e){e.removeLeadSnapshot()}function Pa(e,t,r){e.translate=Le(t.translate,0,r),e.scale=Le(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ka(e,t,r,n){e.min=Le(t.min,r.min,n),e.max=Le(t.max,r.max,n)}function Da(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Ia={duration:.45,ease:[.4,0,.1,1]},Na=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Ma=Na("applewebkit/")&&!Na("chrome/")?Math.round:y;function La(e){e.min=Ma(e.min),e.max=Ma(e.max)}function Fa(e,t,r){return"position"===e||"preserve-aspect"===e&&(n=la(t),i=la(r),s=.2,!(Math.abs(n-i)<=s));var n,i,s}function Ua(e){return e!==e.root&&e.scroll?.wasRoot}const Ba=ga({attachResizeListener:(e,t)=>Wi(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Va={current:void 0},Wa=ga({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Va.current){const e=new Ba({});e.mount(window),e.setOptions({layoutScroll:!0}),Va.current=e}return Va.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),ja={pan:{Feature:class extends Ui{constructor(){super(...arguments),this.removePointerDownListener=y}onPointerDown(e){this.session=new gs(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ps(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:ks(e),onStart:ks(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&Y.postRender((()=>n(e,t)))}}}mount(){this.removePointerDownListener=Hi(this.node.current,"pointerdown",(e=>this.onPointerDown(e)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Ui{constructor(e){super(e),this.removeGroupControls=y,this.removeListeners=y,this.controls=new _s(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||y}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Wa,MeasureLayout:Fs}};function Ha(e,t,r){const{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);const i=n["onHover"+r];i&&Y.postRender((()=>i(t,ji(t))))}function Ga(e,t,r){const{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);const i=n["onTap"+("End"===r?"":r)];i&&Y.postRender((()=>i(t,ji(t))))}const $a=new WeakMap,za=new WeakMap,Ya=e=>{const t=$a.get(e.target);t&&t(e)},Xa=e=>{e.forEach(Ya)};function Ka(e,t,r){const n=function({root:e,...t}){const r=e||document;za.has(r)||za.set(r,{});const n=za.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(Xa,{root:e,...t})),n[i]}(t);return $a.set(e,r),n.observe(e),()=>{$a.delete(e),n.unobserve(e)}}const Ja={some:0,all:1};const qa={inView:{Feature:class extends Ui{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:Ja[n]};return Ka(this.node.current,s,(e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),s=t?r:n;s&&s(e)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Ui{mount(){const{current:e}=this.node;e&&(this.unmount=qr(e,((e,t)=>(Ga(this.node,t,"Start"),(e,{success:t})=>Ga(this.node,e,t?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Ui{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=E(Wi(this.node.current,"focus",(()=>this.onFocus())),Wi(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Ui{mount(){const{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){const[n,i,s]=jr(e,r),a=e=>{if(!Hr(e))return;const{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;const s=e=>{Hr(e)&&(n(e),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,i)};return n.forEach((e=>{e.addEventListener("pointerenter",a,i)})),s}(e,((e,t)=>(Ha(this.node,t,"Start"),e=>Ha(this.node,e,"End")))))}unmount(){}}}},Za={layout:{ProjectionNode:Wa,MeasureLayout:Fs}},Qa={current:null},eo={current:!1};const to=new WeakMap;const ro=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class no{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qt,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const e=Q.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,Y.render(this.render,!1,!0))};const{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=An(t),this.isVariantNode=xn(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(const f in h){const e=h[f];void 0!==o[f]&&Qr(e)&&e.set(o[f],!1)}}mount(e){this.current=e,to.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((e,t)=>this.bindToMotionValue(t,e))),eo.current||function(){if(eo.current=!0,l)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Qa.current=e.matches;e.addListener(t),t()}else Qa.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Qa.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach((e=>e())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const r=jt.has(e);r&&this.onBindTransform&&this.onBindTransform();const n=t.on("change",(t=>{this.latestValues[e]=t,this.props.onUpdate&&Y.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)})),i=t.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,(()=>{n(),i(),s&&s(),t.owner&&t.stop()}))}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in dn){const t=dn[e];if(!t)continue;const{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let r=0;r<ro.length;r++){const t=ro[r];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const n=e["on"+t];n&&(this.propEventSubscriptions[t]=this.on(t,n))}this.prevMotionValues=function(e,t,r){for(const n in t){const i=t[n],s=r[n];if(Qr(i))e.addValue(n,i);else if(Qr(s))e.addValue(n,Ur(i,{owner:e}));else if(s!==i)if(e.hasValue(n)){const t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{const t=e.getStaticValue(n);e.addValue(n,Ur(void 0!==t?t:i,{owner:e}))}}for(const n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){const r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=Ur(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];var n;return null!=r&&("string"==typeof r&&(m(r)||v(r))?r=parseFloat(r):(n=r,!en.find(br(n))&&Ie.test(t)&&(r=Ir(e,t))),this.setBaseTarget(e,Qr(r)?r.get():r)),Qr(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){const{initial:t}=this.props;let r;if("string"==typeof t||"object"==typeof t){const n=ni(this.props,t,this.presenceContext?.custom);n&&(r=n[e])}if(t&&void 0!==r)return r;const n=this.getBaseTargetFromProps(this.props,e);return void 0===n||Qr(n)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new S),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class io extends no{constructor(){super(...arguments),this.KeyframeResolver=Mr}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;Qr(e)&&(this.childSubscription=e.on("change",(e=>{this.current&&(this.current.textContent=`${e}`)})))}}function so(e,{style:t,vars:r},n,i){Object.assign(e.style,t,i&&i.getProjectionStyles(n));for(const s in r)e.style.setProperty(s,r[s])}class ao extends io{constructor(){super(...arguments),this.type="html",this.renderInstance=so}readValueFromInstance(e,t){if(jt.has(t))return this.projection?.isProjecting?Ut(t):((e,t)=>{const{transform:r="none"}=getComputedStyle(e);return Bt(r,t)})(e,t);{const n=(r=e,window.getComputedStyle(r)),i=(te(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof i?i.trim():i}var r}measureInstanceViewportBox(e,{transformPagePoint:t}){return ds(e,t)}build(e,t,r){jn(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return oi(e,t,r)}}const oo=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class lo extends io{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=qi}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(jt.has(t)){const e=Dr(t);return e&&e.default||0}return t=oo.has(t)?t:kn(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return ci(e,t,r)}build(e,t,r){Kn(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){!function(e,t,r,n){so(e,t,void 0,n);for(const i in t.attrs)e.setAttribute(oo.has(i)?i:kn(i),t.attrs[i])}(e,t,0,n)}mount(e){this.isSVGTag=qn(e.tagName),super.mount(e)}}const co=Tn(fi({...Vi,...qa,...ja,...Za},((t,r)=>ei(t)?new lo(r):new ao(r,{allowProjection:t!==e.Fragment}))));function ho(e,t){[...t].reverse().forEach((r=>{const n=e.getVariant(r);n&&mi(e,n),e.variantChildren&&e.variantChildren.forEach((e=>{ho(e,t)}))}))}function fo(){const e=new Set,t={subscribe:t=>(e.add(t),()=>{e.delete(t)}),start(t,r){const n=[];return e.forEach((e=>{n.push(Ri(e,t,{transitionOverride:r}))})),Promise.all(n)},set:t=>e.forEach((e=>{!function(e,t){Array.isArray(t)?ho(e,t):"string"==typeof t?ho(e,[t]):mi(e,t)}(e,t)})),stop(){e.forEach((e=>{!function(e){e.values.forEach((e=>e.stop()))}(e)}))},mount:()=>()=>{t.stop()}};return t}const uo=function(){const e=o(fo);return c(e.mount,[]),e};
/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var po={version:"0.18.5"},mo=1252,go=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],vo=function(e){-1!=go.indexOf(e)&&(mo=e)};var To=function(e){vo(e)};function yo(){To(1200),vo(1252)}var wo,Eo=function(e){return String.fromCharCode(e)},bo=function(e){return String.fromCharCode(e)},So="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Ao(e){for(var t="",r=0,n=0,i=0,s=0,a=0,o=0,l=0,c=0;c<e.length;)s=(r=e.charCodeAt(c++))>>2,a=(3&r)<<4|(n=e.charCodeAt(c++))>>4,o=(15&n)<<2|(i=e.charCodeAt(c++))>>6,l=63&i,isNaN(n)?o=l=64:isNaN(i)&&(l=64),t+=So.charAt(s)+So.charAt(a)+So.charAt(o)+So.charAt(l);return t}function xo(e){var t="",r=0,n=0,i=0,s=0,a=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)r=So.indexOf(e.charAt(l++))<<2|(s=So.indexOf(e.charAt(l++)))>>4,t+=String.fromCharCode(r),n=(15&s)<<4|(a=So.indexOf(e.charAt(l++)))>>2,64!==a&&(t+=String.fromCharCode(n)),i=(3&a)<<6|(o=So.indexOf(e.charAt(l++))),64!==o&&(t+=String.fromCharCode(i));return t}var Co=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),Oo=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function Ro(e){return Co?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function _o(e){return Co?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var Po=function(e){return Co?Oo(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function ko(e){if("undefined"==typeof ArrayBuffer)return Po(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function Do(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var Io=Co?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:Oo(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),i=0;for(t=0,r=0;t<e.length;r+=i,++t)if(i=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var No=/\u0000/g,Mo=/[\u0001-\u0006]/g;function Lo(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Fo(e,t){var r=""+e;return r.length>=t?r:Xl("0",t-r.length)+r}function Uo(e,t){var r=""+e;return r.length>=t?r:Xl(" ",t-r.length)+r}function Bo(e,t){var r=""+e;return r.length>=t?r:r+Xl(" ",t-r.length)}var Vo=Math.pow(2,32);function Wo(e,t){return e>Vo||e<-Vo?function(e,t){var r=""+Math.round(e);return r.length>=t?r:Xl("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:Xl("0",t-r.length)+r}(Math.round(e),t)}function jo(e,t){return t=t||0,e.length>=7+t&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var Ho=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Go=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var $o={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},zo={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Yo={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Xo(e,t,r){for(var n=e<0?-1:1,i=e*n,s=0,a=1,o=0,l=1,c=0,h=0,f=Math.floor(i);c<t&&(o=(f=Math.floor(i))*a+s,h=f*c+l,!(i-f<5e-8));)i=1/(i-f),s=a,a=o,l=c,c=h;if(h>t&&(c>t?(h=l,o=s):(h=c,o=a)),!r)return[0,n*o,h];var u=Math.floor(n*o/h);return[u,n*o-u*h,h]}function Ko(e,t,r){if(e>2958465||e<0)return null;var n=0|e,i=Math.floor(86400*(e-n)),s=0,a=[],o={D:n,T:i,u:86400*(e-n)-i,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++i&&(o.T=i=0,++n,++o.D)),60===n)a=r?[1317,10,29]:[1900,2,29],s=3;else if(0===n)a=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var l=new Date(1900,0,1);l.setDate(l.getDate()+n-1),a=[l.getFullYear(),l.getMonth()+1,l.getDate()],s=l.getDay(),n<60&&(s=(s+6)%7),r&&(s=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(l,a))}return o.y=a[0],o.m=a[1],o.d=a[2],o.S=i%60,i=Math.floor(i/60),o.M=i%60,i=Math.floor(i/60),o.H=i,o.q=s,o}var Jo=new Date(1899,11,31,0,0,0),qo=Jo.getTime(),Zo=new Date(1900,2,1,0,0,0);function Qo(e,t){var r=e.getTime();return t?r-=1262304e5:e>=Zo&&(r+=864e5),(r-(qo+6e4*(e.getTimezoneOffset()-Jo.getTimezoneOffset())))/864e5}function el(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function tl(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=el(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=el(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),el(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function rl(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):tl(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Sl(14,Qo(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function nl(e,t,r,n){var i,s="",a=0,o=0,l=r.y,c=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:i=l%100,c=2;break;default:i=l%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:i=r.m,c=t.length;break;case 3:return Go[r.m-1][1];case 5:return Go[r.m-1][0];default:return Go[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:i=r.d,c=t.length;break;case 3:return Ho[r.q][0];default:return Ho[r.q][1]}break;case 104:switch(t.length){case 1:case 2:i=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:i=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:i=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=n>=2?3===n?1e3:100:1===n?10:1,(a=Math.round(o*(r.S+r.u)))>=60*o&&(a=0),"s"===t?0===a?"0":""+a/o:(s=Fo(a,2+n),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):Fo(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":i=24*r.D+r.H;break;case"[m]":case"[mm]":i=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":i=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:i=l,c=1}return c>0?Fo(i,c):""}function il(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var sl=/%/g;function al(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+al(e,-t);var i=e.indexOf(".");-1===i&&(i=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%i;if(s<0&&(s+=i),-1===(r=(t/Math.pow(10,s)).toPrecision(n+1+(i+s)%i)).indexOf("e")){var a=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(a-r.length+s):r+="E+"+(a-s);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,i)+"."+r.substr(2+i)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(i+s)%i)+"."+n.substr(s)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ol=/# (\?+)( ?)\/( ?)(\d+)/;var ll=/^#*0*\.([0#]+)/,cl=/\).*[0#]/,hl=/\(###\) ###\\?-####/;function fl(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function ul(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function dl(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function pl(e,t,r){if(40===e.charCodeAt(0)&&!t.match(cl)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?pl("n",n,r):"("+pl("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return vl(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(sl,""),i=t.length-n.length;return vl(e,n,r*Math.pow(10,2*i))+Xl("%",i)}(e,t,r);if(-1!==t.indexOf("E"))return al(t,r);if(36===t.charCodeAt(0))return"$"+pl(e,t.substr(" "==t.charAt(1)?2:1),r);var i,s,a,o,l=Math.abs(r),c=r<0?"-":"";if(t.match(/^00+$/))return c+Wo(l,t.length);if(t.match(/^[#?]+$/))return"0"===(i=Wo(r,0))&&(i=""),i.length>t.length?i:fl(t.substr(0,t.length-i.length))+i;if(s=t.match(ol))return function(e,t,r){var n=parseInt(e[4],10),i=Math.round(t*n),s=Math.floor(i/n),a=i-s*n,o=n;return r+(0===s?"":""+s)+" "+(0===a?Xl(" ",e[1].length+1+e[4].length):Uo(a,e[1].length)+e[2]+"/"+e[3]+Fo(o,e[4].length))}(s,l,c);if(t.match(/^#+0+$/))return c+Wo(l,t.length-t.indexOf("0"));if(s=t.match(ll))return i=ul(r,s[1].length).replace(/^([^\.]+)$/,"$1."+fl(s[1])).replace(/\.$/,"."+fl(s[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+Xl("0",fl(s[1]).length-t.length)})),-1!==t.indexOf("0.")?i:i.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return c+ul(l,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return c+il(Wo(l,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+pl(e,t,-r):il(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,s[1].length)))+"."+Fo(dl(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return pl(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return i=Lo(pl(e,t.replace(/[\\-]/g,""),r)),a=0,Lo(Lo(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return a<i.length?i.charAt(a++):"0"===e?"0":""})));if(t.match(hl))return"("+(i=pl(e,"##########",r)).substr(0,3)+") "+i.substr(3,3)+"-"+i.substr(6);var h="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(s[4].length,7),o=Xo(l,Math.pow(10,a)-1,!1),i=""+c," "==(h=vl("n",s[1],o[1])).charAt(h.length-1)&&(h=h.substr(0,h.length-1)+"0"),i+=h+s[2]+"/"+s[3],(h=Bo(o[2],a)).length<s[4].length&&(h=fl(s[4].substr(s[4].length-h.length))+h),i+=h;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(Math.max(s[1].length,s[4].length),7),c+((o=Xo(l,Math.pow(10,a)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?Uo(o[1],a)+s[2]+"/"+s[3]+Bo(o[2],a):Xl(" ",2*a+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return i=Wo(r,0),t.length<=i.length?i:fl(t.substr(0,t.length-i.length))+i;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){i=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),a=i.indexOf(".");var f=t.indexOf(".")-a,u=t.length-i.length-f;return fl(t.substr(0,f)+i+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return a=dl(r,s[1].length),r<0?"-"+pl(e,t,-r):il(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?Fo(0,3-e.length):"")+e}))+"."+Fo(a,s[1].length);switch(t){case"###,##0.00":return pl(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=il(Wo(l,0));return"0"!==d?c+d:"";case"###,###.00":return pl(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return pl(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function ml(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+ml(e,-t);var i=e.indexOf(".");-1===i&&(i=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%i;if(s<0&&(s+=i),!(r=(t/Math.pow(10,s)).toPrecision(n+1+(i+s)%i)).match(/[Ee]/)){var a=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(a-r.length+s):r+="E+"+(a-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(i+s)%i)+"."+n.substr(s)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function gl(e,t,r){if(40===e.charCodeAt(0)&&!t.match(cl)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?gl("n",n,r):"("+gl("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return vl(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(sl,""),i=t.length-n.length;return vl(e,n,r*Math.pow(10,2*i))+Xl("%",i)}(e,t,r);if(-1!==t.indexOf("E"))return ml(t,r);if(36===t.charCodeAt(0))return"$"+gl(e,t.substr(" "==t.charAt(1)?2:1),r);var i,s,a,o,l=Math.abs(r),c=r<0?"-":"";if(t.match(/^00+$/))return c+Fo(l,t.length);if(t.match(/^[#?]+$/))return i=""+r,0===r&&(i=""),i.length>t.length?i:fl(t.substr(0,t.length-i.length))+i;if(s=t.match(ol))return function(e,t,r){return r+(0===t?"":""+t)+Xl(" ",e[1].length+2+e[4].length)}(s,l,c);if(t.match(/^#+0+$/))return c+Fo(l,t.length-t.indexOf("0"));if(s=t.match(ll))return i=(i=(""+r).replace(/^([^\.]+)$/,"$1."+fl(s[1])).replace(/\.$/,"."+fl(s[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+Xl("0",fl(s[1]).length-t.length)})),-1!==t.indexOf("0.")?i:i.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return c+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return c+il(""+l);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+gl(e,t,-r):il(""+r)+"."+Xl("0",s[1].length);if(s=t.match(/^#,#*,#0/))return gl(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return i=Lo(gl(e,t.replace(/[\\-]/g,""),r)),a=0,Lo(Lo(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return a<i.length?i.charAt(a++):"0"===e?"0":""})));if(t.match(hl))return"("+(i=gl(e,"##########",r)).substr(0,3)+") "+i.substr(3,3)+"-"+i.substr(6);var h="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(s[4].length,7),o=Xo(l,Math.pow(10,a)-1,!1),i=""+c," "==(h=vl("n",s[1],o[1])).charAt(h.length-1)&&(h=h.substr(0,h.length-1)+"0"),i+=h+s[2]+"/"+s[3],(h=Bo(o[2],a)).length<s[4].length&&(h=fl(s[4].substr(s[4].length-h.length))+h),i+=h;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(Math.max(s[1].length,s[4].length),7),c+((o=Xo(l,Math.pow(10,a)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?Uo(o[1],a)+s[2]+"/"+s[3]+Bo(o[2],a):Xl(" ",2*a+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return i=""+r,t.length<=i.length?i:fl(t.substr(0,t.length-i.length))+i;if(s=t.match(/^([#0]+)\.([#0]+)$/)){i=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),a=i.indexOf(".");var f=t.indexOf(".")-a,u=t.length-i.length-f;return fl(t.substr(0,f)+i+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+gl(e,t,-r):il(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?Fo(0,3-e.length):"")+e}))+"."+Fo(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=il(""+l);return"0"!==d?c+d:"";default:if(t.match(/\.[0#?]*$/))return gl(e,t.slice(0,t.lastIndexOf(".")),r)+fl(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function vl(e,t,r){return(0|r)===r?gl(e,t,r):pl(e,t,r)}var Tl=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function yl(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":jo(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(Tl))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var wl=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function El(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function bl(e,t){var r=function(e){for(var t=[],r=!1,n=0,i=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(i,n-i),i=n+1}if(t[t.length]=e.substr(i),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),n=r.length,i=r[n-1].indexOf("@");if(n<4&&i>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||i>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=i>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=i>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=i>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var a=r[0].match(wl),o=r[1].match(wl);return El(t,a)?[n,r[0]]:El(t,o)?[n,r[1]]:[n,r[null!=a&&null!=o?2:1]]}return[n,s]}function Sl(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:$o)[e])&&(n=r.table&&r.table[zo[e]]||$o[zo[e]]),null==n&&(n=Yo[e]||"General")}if(jo(n,0))return rl(t,r);t instanceof Date&&(t=Qo(t,r.date1904));var i=bl(n,t);if(jo(i[1]))return rl(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,n){for(var i,s,a,o=[],l="",c=0,h="",f="t",u="H";c<e.length;)switch(h=e.charAt(c)){case"G":if(!jo(e,c))throw new Error("unrecognized character "+h+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(l="";34!==(a=e.charCodeAt(++c))&&c<e.length;)l+=String.fromCharCode(a);o[o.length]={t:"t",v:l},++c;break;case"\\":var d=e.charAt(++c),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==i&&null==(i=Ko(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},f=h,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":h=h.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==i&&null==(i=Ko(t,r)))return"";for(l=h;++c<e.length&&e.charAt(c).toLowerCase()===h;)l+=h;"m"===h&&"h"===f.toLowerCase()&&(h="M"),"h"===h&&(h=u),o[o.length]={t:h,v:l},f=h;break;case"A":case"a":case"上":var m={t:h,v:h};if(null==i&&(i=Ko(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=i&&(m.v=i.H>=12?"P":"A"),m.t="T",u="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=i&&(m.v=i.H>=12?"PM":"AM"),m.t="T",c+=5,u="h"):"上午/下午"===e.substr(c,5).toUpperCase()?(null!=i&&(m.v=i.H>=12?"下午":"上午"),m.t="T",c+=5,u="h"):(m.t="t",++c),null==i&&"T"===m.t)return"";o[o.length]=m,f=h;break;case"[":for(l=h;"]"!==e.charAt(c++)&&c<e.length;)l+=e.charAt(c);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(Tl)){if(null==i&&null==(i=Ko(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},f=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",yl(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=i){for(l=h;++c<e.length&&"0"===(h=e.charAt(c));)l+=h;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=h;++c<e.length&&"0#?.,E+-%".indexOf(h=e.charAt(c))>-1;)l+=h;o[o.length]={t:"n",v:l};break;case"?":for(l=h;e.charAt(++c)===h;)l+=h;o[o.length]={t:h,v:l},f=h;break;case"*":++c," "!=e.charAt(c)&&"*"!=e.charAt(c)||++c;break;case"(":case")":o[o.length]={t:1===n?"t":h,v:h},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=h;c<e.length&&"0123456789".indexOf(e.charAt(++c))>-1;)l+=e.charAt(c);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:h,v:h},++c;break;case"$":o[o.length]={t:"t",v:"$"},++c;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(h))throw new Error("unrecognized character "+h+" in "+e);o[o.length]={t:"t",v:h},++c}var g,v=0,T=0;for(c=o.length-1,f="t";c>=0;--c)switch(o[c].t){case"h":case"H":o[c].t=u,f="h",v<1&&(v=1);break;case"s":(g=o[c].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":f=o[c].t;break;case"m":"s"===f&&(o[c].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[c].v.match(/[Hh]/)&&(v=1),v<2&&o[c].v.match(/[Mm]/)&&(v=2),v<3&&o[c].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:i.u>=.5&&(i.u=0,++i.S),i.S>=60&&(i.S=0,++i.M),i.M>=60&&(i.M=0,++i.H);break;case 2:i.u>=.5&&(i.u=0,++i.S),i.S>=60&&(i.S=0,++i.M)}var y,w="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=nl(o[c].t.charCodeAt(0),o[c].v,i,T),o[c].t="t";break;case"n":case"?":for(y=c+1;null!=o[y]&&("?"===(h=o[y].t)||"D"===h||(" "===h||"t"===h)&&null!=o[y+1]&&("?"===o[y+1].t||"t"===o[y+1].t&&"/"===o[y+1].v)||"("===o[c].t&&(" "===h||"n"===h||")"===h)||"t"===h&&("/"===o[y].v||" "===o[y].v&&null!=o[y+1]&&"?"==o[y+1].t));)o[c].v+=o[y].v,o[y]={v:"",t:";"},++y;w+=o[c].v,c=y-1;break;case"G":o[c].t="t",o[c].v=rl(t,r)}var E,b,S="";if(w.length>0){40==w.charCodeAt(0)?(E=t<0&&45===w.charCodeAt(0)?-t:t,b=vl("n",w,E)):(b=vl("n",w,E=t<0&&n>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(b=b.substr(1),o[0].v="-"+o[0].v)),y=b.length-1;var A=o.length;for(c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&o[c].v.indexOf(".")>-1){A=c;break}var x=o.length;if(A===o.length&&-1===b.indexOf("E")){for(c=o.length-1;c>=0;--c)null!=o[c]&&-1!=="n?".indexOf(o[c].t)&&(y>=o[c].v.length-1?(y-=o[c].v.length,o[c].v=b.substr(y+1,o[c].v.length)):y<0?o[c].v="":(o[c].v=b.substr(0,y+1),y=-1),o[c].t="t",x=c);y>=0&&x<o.length&&(o[x].v=b.substr(0,y+1)+o[x].v)}else if(A!==o.length&&-1===b.indexOf("E")){for(y=b.indexOf(".")-1,c=A;c>=0;--c)if(null!=o[c]&&-1!=="n?".indexOf(o[c].t)){for(s=o[c].v.indexOf(".")>-1&&c===A?o[c].v.indexOf(".")-1:o[c].v.length-1,S=o[c].v.substr(s+1);s>=0;--s)y>=0&&("0"===o[c].v.charAt(s)||"#"===o[c].v.charAt(s))&&(S=b.charAt(y--)+S);o[c].v=S,o[c].t="t",x=c}for(y>=0&&x<o.length&&(o[x].v=b.substr(0,y+1)+o[x].v),y=b.indexOf(".")+1,c=A;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===A)){for(s=o[c].v.indexOf(".")>-1&&c===A?o[c].v.indexOf(".")+1:0,S=o[c].v.substr(0,s);s<o[c].v.length;++s)y<b.length&&(S+=b.charAt(y++));o[c].v=S,o[c].t="t",x=c}}}for(c=0;c<o.length;++c)null!=o[c]&&"n?".indexOf(o[c].t)>-1&&(E=n>1&&t<0&&c>0&&"-"===o[c-1].v?-t:t,o[c].v=vl(o[c].t,o[c].v,E),o[c].t="t");var C="";for(c=0;c!==o.length;++c)null!=o[c]&&(C+=o[c].v);return C}(i[1],t,r,i[0])}function Al(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=$o[r]){if($o[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return $o[t]=e,t}function xl(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Al(e[t],t)}function Cl(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',$o=e}var Ol=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var Rl=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,n=0,i="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(n=0;256!=n;++n)i[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=i[t]=r>>>8^e[255&r];var s=[];for(n=1;16!=n;++n)s[n-1]="undefined"!=typeof Int32Array?i.subarray(256*n,256*n+256):i.slice(256*n,256*n+256);return s}(t),n=r[0],i=r[1],s=r[2],a=r[3],o=r[4],l=r[5],c=r[6],h=r[7],f=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var n=-1^r,i=0,s=e.length;i<s;)n=n>>>8^t[255&(n^e.charCodeAt(i++))];return~n},e.buf=function(e,r){for(var T=-1^r,y=e.length-15,w=0;w<y;)T=v[e[w++]^255&T]^g[e[w++]^T>>8&255]^m[e[w++]^T>>16&255]^p[e[w++]^T>>>24]^d[e[w++]]^u[e[w++]]^f[e[w++]]^h[e[w++]]^c[e[w++]]^l[e[w++]]^o[e[w++]]^a[e[w++]]^s[e[w++]]^i[e[w++]]^n[e[w++]]^t[e[w++]];for(y+=15;w<y;)T=T>>>8^t[255&(T^e[w++])];return~T},e.str=function(e,r){for(var n=-1^r,i=0,s=e.length,a=0,o=0;i<s;)(a=e.charCodeAt(i++))<128?n=n>>>8^t[255&(n^a)]:a<2048?n=(n=n>>>8^t[255&(n^(192|a>>6&31))])>>>8^t[255&(n^(128|63&a))]:a>=55296&&a<57344?(a=64+(1023&a),o=1023&e.charCodeAt(i++),n=(n=(n=(n=n>>>8^t[255&(n^(240|a>>8&7))])>>>8^t[255&(n^(128|a>>2&63))])>>>8^t[255&(n^(128|o>>6&15|(3&a)<<4))])>>>8^t[255&(n^(128|63&o))]):n=(n=(n=n>>>8^t[255&(n^(224|a>>12&15))])>>>8^t[255&(n^(128|a>>6&63))])>>>8^t[255&(n^(128|63&a))];return~n},e}(),_l=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function i(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=(n=n<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,n)}function s(e){yh(e,0);for(var t={},r=0;e.l<=e.length-4;){var n=e.read_shift(2),i=e.read_shift(2),s=e.l+i,a={};if(21589===n)1&(r=e.read_shift(1))&&(a.mtime=e.read_shift(4)),i>5&&(2&r&&(a.atime=e.read_shift(4)),4&r&&(a.ctime=e.read_shift(4))),a.mtime&&(a.mt=new Date(1e3*a.mtime));e.l=s,t[n]=a}return t}function a(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return ie(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=b(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",n=(Co&&Buffer.isBuffer(e)?e.toString("binary"):b(e)).split("\r\n"),i=0,s="";for(i=0;i<n.length;++i)if(s=n[i],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var a=(n[1]||"").match(/boundary="(.*?)"/);if(!a)throw new Error("MAD cannot find boundary");var o="--"+(a[1]||""),l=[],c=[],h={FileIndex:l,FullPaths:c};u(h);var f,d=0;for(i=0;i<n.length;++i){var p=n[i];p!==o&&p!==o+"--"||(d++&&he(h,n.slice(f,i),r),f=i)}return h}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,n,i,s,a,o,d=512,p=[],m=e.slice(0,512);yh(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(T,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==g[1])return ie(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==d&&yh(m=e.slice(0,d),28);var y=e.slice(0,d);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var w=m.read_shift(4,"i");if(3===r&&0!==w)throw new Error("# Directory Sectors: Expected 0 saw "+w);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),a=m.read_shift(4,"i"),n=m.read_shift(4,"i"),o=m.read_shift(4,"i"),i=m.read_shift(4,"i");for(var E=-1,S=0;S<109&&!((E=m.read_shift(4,"i"))<0);++S)p[S]=E;var A=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],i=1;i<r;++i)n[i-1]=e.slice(i*t,(i+1)*t);return n[r-1]=e.slice(r*t),n}(e,d);c(o,i,A,d,p);var x=function(e,t,r,n){var i=e.length,s=[],a=[],o=[],l=[],c=n-1,h=0,f=0,u=0,d=0;for(h=0;h<i;++h)if(o=[],(u=h+t)>=i&&(u-=i),!a[u]){l=[];var p=[];for(f=u;f>=0;){p[f]=!0,a[f]=!0,o[o.length]=f,l.push(e[f]);var m=r[Math.floor(4*f/n)];if(n<4+(d=4*f&c))throw new Error("FAT boundary crossed: "+f+" 4 "+n);if(!e[m])break;if(p[f=fh(e[m],d)])break}s[u]={nodes:o,data:Wc([l])}}return s}(A,s,p,d);x[s].name="!Directory",n>0&&a!==v&&(x[a].name="!MiniFAT"),x[p[0]].name="!FAT",x.fat_addrs=p,x.ssz=d;var C=[],O=[],R=[];!function(e,t,r,n,i,s,a,o){for(var c,u=0,d=n.length?2:0,p=t[e].data,m=0,g=0;m<p.length;m+=128){var T=p.slice(m,m+128);yh(T,64),g=T.read_shift(2),c=Hc(T,0,g-d),n.push(c);var y={name:c,type:T.read_shift(1),color:T.read_shift(1),L:T.read_shift(4,"i"),R:T.read_shift(4,"i"),C:T.read_shift(4,"i"),clsid:T.read_shift(16),state:T.read_shift(4,"i"),start:0,size:0};0!==T.read_shift(2)+T.read_shift(2)+T.read_shift(2)+T.read_shift(2)&&(y.ct=f(T,T.l-8)),0!==T.read_shift(2)+T.read_shift(2)+T.read_shift(2)+T.read_shift(2)&&(y.mt=f(T,T.l-8)),y.start=T.read_shift(4,"i"),y.size=T.read_shift(4,"i"),y.size<0&&y.start<0&&(y.size=y.type=0,y.start=v,y.name=""),5===y.type?(u=y.start,i>0&&u!==v&&(t[u].name="!StreamData")):y.size>=4096?(y.storage="fat",void 0===t[y.start]&&(t[y.start]=h(r,y.start,t.fat_addrs,t.ssz)),t[y.start].name=y.name,y.content=t[y.start].data.slice(0,y.size)):(y.storage="minifat",y.size<0?y.size=0:u!==v&&y.start!==v&&t[u]&&(y.content=l(y,t[u].data,(t[o]||{}).data))),y.content&&yh(y.content,0),s[c]=y,a.push(y)}}(s,x,A,C,n,{},O,a),function(e,t,r){for(var n=0,i=0,s=0,a=0,o=0,l=r.length,c=[],h=[];n<l;++n)c[n]=h[n]=n,t[n]=r[n];for(;o<h.length;++o)i=e[n=h[o]].L,s=e[n].R,a=e[n].C,c[n]===n&&(-1!==i&&c[i]!==i&&(c[n]=c[i]),-1!==s&&c[s]!==s&&(c[n]=c[s])),-1!==a&&(c[a]=n),-1!==i&&n!=c[n]&&(c[i]=c[n],h.lastIndexOf(i)<o&&h.push(i)),-1!==s&&n!=c[n]&&(c[s]=c[n],h.lastIndexOf(s)<o&&h.push(s));for(n=1;n<l;++n)c[n]===n&&(-1!==s&&c[s]!==s?c[n]=c[s]:-1!==i&&c[i]!==i&&(c[n]=c[i]));for(n=1;n<l;++n)if(0!==e[n].type){if((o=n)!=c[o])do{o=c[o],t[n]=t[o]+"/"+t[n]}while(0!==o&&-1!==c[o]&&o!=c[o]);c[n]=-1}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}(O,R,C),C.shift();var _={FileIndex:O,FullPaths:R};return t&&t.raw&&(_.raw={header:y,sectors:A}),_}function l(e,t,r){for(var n=e.start,i=e.size,s=[],a=n;r&&i>0&&a>=0;)s.push(t.slice(a*g,a*g+g)),i-=g,a=fh(r,4*a);return 0===s.length?Eh(0):Io(s).slice(0,e.size)}function c(e,t,r,n,i){var s=v;if(e===v){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var a=r[e],o=(n>>>2)-1;if(!a)return;for(var l=0;l<o&&(s=fh(a,4*l))!==v;++l)i.push(s);c(fh(a,n-4),t-1,r,n,i)}}function h(e,t,r,n,i){var s=[],a=[];i||(i=[]);var o=n-1,l=0,c=0;for(l=t;l>=0;){i[l]=!0,s[s.length]=l,a.push(e[l]);var h=r[Math.floor(4*l/n)];if(n<4+(c=4*l&o))throw new Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[h])break;l=fh(e[h],c)}return{nodes:s,data:Wc([a])}}function f(e,t){return new Date(1e3*(hh(e,t+4)/1e7*Math.pow(2,32)+hh(e,t)/1e7-11644473600))}function u(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="Sh33tJ5";if(_l.find(e,"/"+t))return;var r=Eh(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),d(e)}(e)}function d(e,t){u(e);for(var i=!1,s=!1,a=e.FullPaths.length-1;a>=0;--a){var o=e.FileIndex[a];switch(o.type){case 0:s?i=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(i=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(i=!0);break;default:i=!0}}if(i||t){var l=new Date(1987,1,19),c=0,h=Object.create?Object.create(null):{},f=[];for(a=0;a<e.FullPaths.length;++a)h[e.FullPaths[a]]=!0,0!==e.FileIndex[a].type&&f.push([e.FullPaths[a],e.FileIndex[a]]);for(a=0;a<f.length;++a){var d=r(f[a][0]);(s=h[d])||(f.push([d,{name:n(d).replace("/",""),type:1,clsid:w,ct:l,mt:l,content:null}]),h[d]=!0)}for(f.sort((function(e,t){return function(e,t){for(var r=e.split("/"),n=t.split("/"),i=0,s=0,a=Math.min(r.length,n.length);i<a;++i){if(s=r[i].length-n[i].length)return s;if(r[i]!=n[i])return r[i]<n[i]?-1:1}return r.length-n.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],a=0;a<f.length;++a)e.FullPaths[a]=f[a][0],e.FileIndex[a]=f[a][1];for(a=0;a<f.length;++a){var p=e.FileIndex[a],m=e.FullPaths[a];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||w,0===a)p.C=f.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=a+1;c<f.length&&r(e.FullPaths[c])!=m;++c);for(p.C=c>=f.length?-1:c,c=a+1;c<f.length&&r(e.FullPaths[c])!=r(m);++c);p.R=c>=f.length?-1:c,p.type=1}else r(e.FullPaths[a+1]||"")==r(m)&&(p.R=a+1),p.type=2}}}function p(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},n=r.boundary||"SheetJS",i=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(n="------="+n).slice(2)+'"',"","",""],s=e.FullPaths[0],a=s,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(a=e.FullPaths[l].slice(s.length),(o=e.FileIndex[l]).size&&o.content&&"Sh33tJ5"!=a){a=a.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var c=o.content,h=Co&&Buffer.isBuffer(c)?c.toString("binary"):b(c),f=0,u=Math.min(1024,h.length),d=0,p=0;p<=u;++p)(d=h.charCodeAt(p))>=32&&d<128&&++f;var m=f>=4*u/5;i.push(n),i.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+a),i.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),i.push("Content-Type: "+oe(o,a)),i.push(""),i.push(m?ce(h):le(h))}return i.push(n+"--\r\n"),i.join("\r\n")}(e,r);if(d(e),"zip"===r.fileType)return function(e,t){var r=t||{},n=[],s=[],a=Eh(1),o=r.compression?8:0,l=0,c=0,h=0,f=0,u=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],g=[],v=0;for(c=1;c<e.FullPaths.length;++c)if(p=e.FullPaths[c].slice(d.length),(m=e.FileIndex[c]).size&&m.content&&"Sh33tJ5"!=p){var T=f,y=Eh(p.length);for(h=0;h<p.length;++h)y.write_shift(1,127&p.charCodeAt(h));y=y.slice(0,y.l),g[u]=Rl.buf(m.content,0);var w=m.content;8==o&&(w=S(w)),(a=Eh(30)).write_shift(4,67324752),a.write_shift(2,20),a.write_shift(2,l),a.write_shift(2,o),m.mt?i(a,m.mt):a.write_shift(4,0),a.write_shift(-4,g[u]),a.write_shift(4,w.length),a.write_shift(4,m.content.length),a.write_shift(2,y.length),a.write_shift(2,0),f+=a.length,n.push(a),f+=y.length,n.push(y),f+=w.length,n.push(w),(a=Eh(46)).write_shift(4,33639248),a.write_shift(2,0),a.write_shift(2,20),a.write_shift(2,l),a.write_shift(2,o),a.write_shift(4,0),a.write_shift(-4,g[u]),a.write_shift(4,w.length),a.write_shift(4,m.content.length),a.write_shift(2,y.length),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(4,0),a.write_shift(4,T),v+=a.l,s.push(a),v+=y.length,s.push(y),++u}return a=Eh(22),a.write_shift(4,101010256),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,u),a.write_shift(2,u),a.write_shift(4,v),a.write_shift(4,f),a.write_shift(2,0),Io([Io(n),Io(s),a])}(e,r);var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var i=e.FileIndex[n];if(i.content){var s=i.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var a=e.FullPaths.length+3>>2,o=t+127>>7,l=(t+7>>3)+r+a+o,c=l+127>>7,h=c<=109?0:Math.ceil((c-109)/127);l+c+h+127>>7>c;)h=++c<=109?0:Math.ceil((c-109)/127);var f=[1,h,c,o,a,r,t,0];return e.FileIndex[0].size=t<<6,f[7]=(e.FileIndex[0].start=f[0]+f[1]+f[2]+f[3]+f[4]+f[5])+(f[6]+7>>3),f}(e),s=Eh(n[7]<<9),a=0,o=0;for(a=0;a<8;++a)s.write_shift(1,y[a]);for(a=0;a<8;++a)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),a=0;a<3;++a)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:v),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:v),s.write_shift(4,n[1]),a=0;a<109;++a)s.write_shift(-4,a<n[2]?n[1]+a:-1);if(n[1])for(o=0;o<n[1];++o){for(;a<236+127*o;++a)s.write_shift(-4,a<n[2]?n[1]+a:-1);s.write_shift(-4,o===n[1]-1?v:o+1)}var l=function(e){for(o+=e;a<o-1;++a)s.write_shift(-4,a+1);e&&(++a,s.write_shift(-4,v))};for(o=a=0,o+=n[1];a<o;++a)s.write_shift(-4,E.DIFSECT);for(o+=n[2];a<o;++a)s.write_shift(-4,E.FATSECT);l(n[3]),l(n[4]);for(var c=0,h=0,f=e.FileIndex[0];c<e.FileIndex.length;++c)(f=e.FileIndex[c]).content&&((h=f.content.length)<4096||(f.start=o,l(h+511>>9)));for(l(n[6]+7>>3);511&s.l;)s.write_shift(-4,E.ENDOFCHAIN);for(o=a=0,c=0;c<e.FileIndex.length;++c)(f=e.FileIndex[c]).content&&(!(h=f.content.length)||h>=4096||(f.start=o,l(h+63>>6)));for(;511&s.l;)s.write_shift(-4,E.ENDOFCHAIN);for(a=0;a<n[4]<<2;++a){var u=e.FullPaths[a];if(u&&0!==u.length){f=e.FileIndex[a],0===a&&(f.start=f.size?f.start-1:v);var p=0===a&&r.root||f.name;if(h=2*(p.length+1),s.write_shift(64,p,"utf16le"),s.write_shift(2,h),s.write_shift(1,f.type),s.write_shift(1,f.color),s.write_shift(-4,f.L),s.write_shift(-4,f.R),s.write_shift(-4,f.C),f.clsid)s.write_shift(16,f.clsid,"hex");else for(c=0;c<4;++c)s.write_shift(4,0);s.write_shift(4,f.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,f.start),s.write_shift(4,f.size),s.write_shift(4,0)}else{for(c=0;c<17;++c)s.write_shift(4,0);for(c=0;c<3;++c)s.write_shift(4,-1);for(c=0;c<12;++c)s.write_shift(4,0)}}for(a=1;a<e.FileIndex.length;++a)if((f=e.FileIndex[a]).size>=4096)if(s.l=f.start+1<<9,Co&&Buffer.isBuffer(f.content))f.content.copy(s,s.l,0,f.size),s.l+=f.size+511&-512;else{for(c=0;c<f.size;++c)s.write_shift(1,f.content[c]);for(;511&c;++c)s.write_shift(1,0)}for(a=1;a<e.FileIndex.length;++a)if((f=e.FileIndex[a]).size>0&&f.size<4096)if(Co&&Buffer.isBuffer(f.content))f.content.copy(s,s.l,0,f.size),s.l+=f.size+63&-64;else{for(c=0;c<f.size;++c)s.write_shift(1,f.content[c]);for(;63&c;++c)s.write_shift(1,0)}if(Co)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}t.version="1.2.1";var m,g=64,v=-2,T="d0cf11e0a1b11ae1",y=[208,207,17,224,161,177,26,225],w="00000000000000000000000000000000",E={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:v,FREESECT:-1,HEADER_SIGNATURE:T,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:w,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function b(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function S(e){return m?m.deflateRawSync(e):K(e)}var A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],x=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],C=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var O,R,_="undefined"!=typeof Uint8Array,P=_?new Uint8Array(256):[],k=0;k<256;++k)P[k]=(R=void 0,255&((R=139536&((O=k)<<1|O<<11)|558144&(O<<5|O<<15))>>16|R>>8|R));function D(e,t){var r=P[255&e];return t<=8?r>>>8-t:(r=r<<8|P[e>>8&255],t<=16?r>>>16-t:(r=r<<8|P[e>>16&255])>>>24-t)}function I(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}function N(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function M(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function L(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function F(e,t,r){var n=7&t,i=t>>>3,s=(1<<r)-1,a=e[i]>>>n;return r<8-n?a&s:(a|=e[i+1]<<8-n,r<16-n?a&s:(a|=e[i+2]<<16-n,r<24-n?a&s:(a|=e[i+3]<<24-n)&s))}function U(e,t,r){var n=7&t,i=t>>>3;return n<=5?e[i]|=(7&r)<<n:(e[i]|=r<<n&255,e[i+1]=(7&r)>>8-n),t+3}function B(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function V(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=r,t+8}function W(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=255&r,e[n+2]=r>>>8,t+16}function j(e,t){var r=e.length,n=2*r>t?2*r:t+5,i=0;if(r>=t)return e;if(Co){var s=_o(n);if(e.copy)e.copy(s);else for(;i<e.length;++i)s[i]=e[i];return s}if(_){var a=new Uint8Array(n);if(a.set)a.set(e);else for(;i<r;++i)a[i]=e[i];return a}return e.length=n,e}function H(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function G(e,t,r){var n=1,i=0,s=0,a=0,o=0,l=e.length,c=_?new Uint16Array(32):H(32);for(s=0;s<32;++s)c[s]=0;for(s=l;s<r;++s)e[s]=0;l=e.length;var h=_?new Uint16Array(l):H(l);for(s=0;s<l;++s)c[i=e[s]]++,n<i&&(n=i),h[s]=0;for(c[0]=0,s=1;s<=n;++s)c[s+16]=o=o+c[s-1]<<1;for(s=0;s<l;++s)0!=(o=e[s])&&(h[s]=c[o+16]++);var f=0;for(s=0;s<l;++s)if(0!=(f=e[s]))for(o=D(h[s],n)>>n-f,a=(1<<n+4-f)-1;a>=0;--a)t[o|a<<f]=15&f|s<<4;return n}var $=_?new Uint16Array(512):H(512),z=_?new Uint16Array(32):H(32);if(!_){for(var Y=0;Y<512;++Y)$[Y]=0;for(Y=0;Y<32;++Y)z[Y]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);G(e,z,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);G(r,$,288)}();var X=function(){for(var e=_?new Uint8Array(32768):[],t=0,r=0;t<C.length-1;++t)for(;r<C[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=_?new Uint8Array(259):[];for(t=0,r=0;t<x.length-1;++t)for(;r<x[t+1];++r)n[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var n=Math.min(65535,e.length-r),i=r+n==e.length;for(t.write_shift(1,+i),t.write_shift(2,n),t.write_shift(2,65535&~n);n-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var i=0,s=0,a=_?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(i=U(r,i,+!(s+o!=t.length)))&&(i+=8-(7&i)),r.l=i/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];i=8*r.l}else{i=U(r,i,+!(s+o!=t.length)+2);for(var l=0;o-- >0;){var c=t[s],h=-1,f=0;if((h=a[l=32767&(l<<5^c)])&&((h|=-32768&s)>s&&(h-=32768),h<s))for(;t[h+f]==t[s+f]&&f<250;)++f;if(f>2){(c=n[f])<=22?i=V(r,i,P[c+1]>>1)-1:(V(r,i,3),V(r,i+=5,P[c-23]>>5),i+=3);var u=c<8?0:c-4>>2;u>0&&(W(r,i,f-x[c]),i+=u),c=e[s-h],i=V(r,i,P[c]>>3),i-=3;var d=c<4?0:c-2>>1;d>0&&(W(r,i,s-h-C[c]),i+=d);for(var p=0;p<f;++p)a[l]=32767&s,l=32767&(l<<5^t[s]),++s;o-=f-1}else c<=143?c+=48:i=B(r,i,1),i=V(r,i,P[c]),a[l]=32767&s,++s}i=V(r,i,0)-1}}return r.l=(i+7)/8|0,r.l}(t,r)}}();function K(e){var t=Eh(50+Math.floor(1.1*e.length)),r=X(e,t);return t.slice(0,r)}var J=_?new Uint16Array(32768):H(32768),q=_?new Uint16Array(32768):H(32768),Z=_?new Uint16Array(128):H(128),Q=1,ee=1;function te(e,t){var r=M(e,t)+257,n=M(e,t+=5)+1,i=function(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=4?0:e[n+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var s=0,a=_?new Uint8Array(19):H(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=1,c=_?new Uint8Array(8):H(8),h=_?new Uint8Array(8):H(8),f=a.length,u=0;u<i;++u)a[A[u]]=s=N(e,t),l<s&&(l=s),c[s]++,t+=3;var d=0;for(c[0]=0,u=1;u<=l;++u)h[u]=d=d+c[u-1]<<1;for(u=0;u<f;++u)0!=(d=a[u])&&(o[u]=h[d]++);var p=0;for(u=0;u<f;++u)if(0!=(p=a[u])){d=P[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)Z[d|m<<p]=7&p|u<<3}var g=[];for(l=1;g.length<r+n;)switch(t+=7&(d=Z[L(e,t)]),d>>>=3){case 16:for(s=3+I(e,t),t+=2,d=g[g.length-1];s-- >0;)g.push(d);break;case 17:for(s=3+N(e,t),t+=3;s-- >0;)g.push(0);break;case 18:for(s=11+L(e,t),t+=7;s-- >0;)g.push(0);break;default:g.push(d),l<d&&(l=d)}var v=g.slice(0,r),T=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=n;u<30;++u)T[u]=0;return Q=G(v,J,286),ee=G(T,q,30),t}function re(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[Ro(t),2];for(var r=0,n=0,i=_o(t||1<<18),s=0,a=i.length>>>0,o=0,l=0;!(1&n);)if(n=N(e,r),r+=3,n>>>1!=0)for(n>>1==1?(o=9,l=5):(r=te(e,r),o=Q,l=ee);;){!t&&a<s+32767&&(a=(i=j(i,s+32767)).length);var c=F(e,r,o),h=n>>>1==1?$[c]:J[c];if(r+=15&h,(h>>>=4)>>>8&255){if(256==h)break;var f=(h-=257)<8?0:h-4>>2;f>5&&(f=0);var u=s+x[h];f>0&&(u+=F(e,r,f),r+=f),c=F(e,r,l),r+=15&(h=n>>>1==1?z[c]:q[c]);var d=(h>>>=4)<4?0:h-2>>1,p=C[h];for(d>0&&(p+=F(e,r,d),r+=d),!t&&a<u&&(a=(i=j(i,u+100)).length);s<u;)i[s]=i[s-p],++s}else i[s++]=h}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,m>0)for(!t&&a<s+m&&(a=(i=j(i,s+m)).length);m-- >0;)i[s++]=e[r>>>3],r+=8}return t?[i,r+7>>>3]:[i.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ne(e,t){if(!e)throw new Error(t)}function ie(e,t){var r=e;yh(r,0);var n={FileIndex:[],FullPaths:[]};u(n,{root:t.root});for(var i=r.length-4;(80!=r[i]||75!=r[i+1]||5!=r[i+2]||6!=r[i+3])&&i>=0;)--i;r.l=i+4,r.l+=4;var a=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,i=0;i<a;++i){r.l+=20;var l=r.read_shift(4),c=r.read_shift(4),h=r.read_shift(2),f=r.read_shift(2),d=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=s(r.slice(r.l+h,r.l+h+f));r.l+=h+f+d;var g=r.l;r.l=p+4,se(r,l,c,n,m),r.l=g}return n}function se(e,t,r,n,i){e.l+=2;var a=e.read_shift(2),o=e.read_shift(2),l=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,i=31&r,s=15&(r>>>=5);r>>>=4,n.setMilliseconds(0),n.setFullYear(r+1980),n.setMonth(s-1),n.setDate(i);var a=31&t,o=63&(t>>>=5);return t>>>=6,n.setHours(t),n.setMinutes(o),n.setSeconds(a<<1),n}(e);if(8257&a)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var c=e.read_shift(4),h=e.read_shift(4),f=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<f;++p)d+=String.fromCharCode(e[e.l++]);if(u){var g=s(e.slice(e.l,e.l+u));(g[21589]||{}).mt&&(l=g[21589].mt),((i||{})[21589]||{}).mt&&(l=i[21589].mt)}e.l+=u;var v=e.slice(e.l,e.l+c);switch(o){case 8:v=function(e,t){if(!m)return re(e,t);var r=new(0,m.InflateRaw),n=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,n}(e,h);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var T=!1;8&a&&(134695760==e.read_shift(4)&&(e.read_shift(4),T=!0),c=e.read_shift(4),h=e.read_shift(4)),c!=t&&ne(T,"Bad compressed size: "+t+" != "+c),h!=r&&ne(T,"Bad uncompressed size: "+r+" != "+h),fe(n,d,v,{unsafe:!0,mt:l})}var ae={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function oe(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&ae[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/))&&ae[n[1]]?ae[n[1]]:"application/octet-stream"}function le(e){for(var t=Ao(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}function ce(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],n=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),i=0;i<n.length;++i){var s=n[i];if(0!=s.length)for(var a=0;a<s.length;){var o=76,l=s.slice(a,a+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=s.slice(a,a+o),(a+=o)<s.length&&(l+="="),r.push(l)}else r.push("")}return r.join("\r\n")}function he(e,t,r){for(var n,i="",s="",a="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var c=l.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":i=c[2].trim();break;case"content-type":a=c[2].trim();break;case"content-transfer-encoding":s=c[2].trim()}}switch(++o,s.toLowerCase()){case"base64":n=Po(xo(t.slice(o).join("")));break;case"quoted-printable":n=function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r];r<=e.length&&"="==n.charAt(n.length-1);)n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var i=0;i<t.length;++i)t[i]=t[i].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return Po(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var h=fe(e,i.slice(r.length),n,{unsafe:!0});a&&(h.ctype=a)}function fe(e,t,r,i){var s=i&&i.unsafe;s||u(e);var a=!s&&_l.find(e,t);if(!a){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),a={name:n(t),type:2},e.FileIndex.push(a),e.FullPaths.push(o),s||_l.utils.cfb_gc(e)}return a.content=r,a.size=r?r.length:0,i&&(i.CLSID&&(a.clsid=i.CLSID),i.mt&&(a.mt=i.mt),i.ct&&(a.ct=i.ct)),a}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),n=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),i=!1;47===t.charCodeAt(0)?(i=!0,t=r[0].slice(0,-1)+t):i=-1!==t.indexOf("/");var s=t.toUpperCase(),a=!0===i?r.indexOf(s):n.indexOf(s);if(-1!==a)return e.FileIndex[a];var o=!s.match(Mo);for(s=s.replace(No,""),o&&(s=s.replace(Mo,"!")),a=0;a<r.length;++a){if((o?r[a].replace(Mo,"!"):r[a]).replace(No,"")==s)return e.FileIndex[a];if((o?n[a].replace(Mo,"!"):n[a]).replace(No,"")==s)return e.FileIndex[a]}return null},t.read=function(t,r){var n=r&&r.type;switch(n||Co&&Buffer.isBuffer(t)&&(n="buffer"),n||"base64"){case"file":return function(t,r){return a(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(Po(xo(t)),r);case"binary":return o(Po(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var n=p(t,r);switch(r&&r.type||"buffer"){case"file":return a(),e.writeFileSync(r.filename,n),n;case"binary":return"string"==typeof n?n:b(n);case"base64":return Ao("string"==typeof n?n:b(n));case"buffer":if(Co)return Buffer.isBuffer(n)?n:Oo(n);case"array":return"string"==typeof n?Po(n):n}return n},t.writeFile=function(t,r,n){a();var i=p(t,n);e.writeFileSync(r,i)},t.utils={cfb_new:function(e){var t={};return u(t,e),t},cfb_add:fe,cfb_del:function(e,t){u(e);var r=_l.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1},cfb_mov:function(e,t,r){u(e);var i=_l.find(e,t);if(i)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==i)return e.FileIndex[s].name=n(r),e.FullPaths[s]=r,!0;return!1},cfb_gc:function(e){d(e,!0)},ReadShift:dh,CheckField:Th,prep_blob:yh,bconcat:Io,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");m=e}catch(r){}},_deflateRaw:K,_inflateRaw:re,consts:E},t}();function Pl(e){return"string"==typeof e?ko(e):Array.isArray(e)?function(e){if("undefined"==typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function kl(e,t,r){if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=ko(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?uc(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var i=new Blob([Pl(n)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(i,e);if("undefined"!=typeof saveAs)return saveAs(i,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(i);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var a=document.createElement("a");if(null!=a.download)return a.download=e,a.href=s,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),s}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=Do(t)),o.write(t),o.close(),t}catch(l){if(!l.message||!l.message.match(/onstruct/))throw l}throw new Error("cannot save file "+e)}function Dl(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function Il(e,t){for(var r=[],n=Dl(e),i=0;i!==n.length;++i)null==r[e[n[i]][t]]&&(r[e[n[i]][t]]=n[i]);return r}function Nl(e){for(var t=[],r=Dl(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Ml(e){for(var t=[],r=Dl(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var Ll=new Date(1899,11,30,0,0,0);function Fl(e,t){return(e.getTime()-(Ll.getTime()+6e4*(e.getTimezoneOffset()-Ll.getTimezoneOffset())))/864e5}var Ul=new Date,Bl=Ll.getTime()+6e4*(Ul.getTimezoneOffset()-Ll.getTimezoneOffset()),Vl=Ul.getTimezoneOffset();function Wl(e){var t=new Date;return t.setTime(24*e*60*60*1e3+Bl),t.getTimezoneOffset()!==Vl&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-Vl)),t}var jl=new Date("2017-02-19T19:06:09.000Z"),Hl=isNaN(jl.getFullYear())?new Date("2/19/17"):jl,Gl=2017==Hl.getFullYear();function $l(e,t){var r=new Date(e);if(Gl)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==Hl.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var i=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+i[0],+i[1]-1,+i[2],+i[3]||0,+i[4]||0,+i[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function zl(e,t){if(Co&&Buffer.isBuffer(e))return e.toString("binary");if("undefined"!=typeof TextDecoder)try{var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(s){}for(var n=[],i=0;i!=e.length;++i)n.push(String.fromCharCode(e[i]));return n.join("")}function Yl(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Yl(e[r]));return t}function Xl(e,t){for(var r="";r.length<t;)r+=e;return r}function Kl(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(n))?(n=n.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(n))?t:t/r):t/r}var Jl=["january","february","march","april","may","june","july","august","september","october","november","december"];function ql(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),i=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var a=e.toLowerCase();if(a.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((a=a.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==Jl.indexOf(a))return r}else if(a.match(/[a-z]/))return r;return n<0||n>8099?r:(i>0||s>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Zl(e,t,r){if(e.FullPaths){var n;if("string"==typeof r)return n=Co?Oo(r):function(e){for(var t=[],r=0,n=e.length+250,i=Ro(e.length+255),s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a<128)i[r++]=a;else if(a<2048)i[r++]=192|a>>6&31,i[r++]=128|63&a;else if(a>=55296&&a<57344){a=64+(1023&a);var o=1023&e.charCodeAt(++s);i[r++]=240|a>>8&7,i[r++]=128|a>>2&63,i[r++]=128|o>>6&15|(3&a)<<4,i[r++]=128|63&o}else i[r++]=224|a>>12&15,i[r++]=128|a>>6&63,i[r++]=128|63&a;r>n&&(t.push(i.slice(0,r)),r=0,i=Ro(65535),n=65530)}return t.push(i.slice(0,r)),Io(t)}(r),_l.utils.cfb_add(e,t,n);_l.utils.cfb_add(e,t,r)}else e.file(t,r)}function Ql(){return _l.utils.cfb_new()}var ec='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',tc=Nl({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),rc=/[&<>'"]/g,nc=/[\u0000-\u0008\u000b-\u001f]/g;function ic(e){return(e+"").replace(rc,(function(e){return tc[e]})).replace(nc,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function sc(e){return ic(e).replace(/ /g,"_x0020_")}var ac=/[\u0000-\u001f]/g;function oc(e){for(var t="",r=0,n=0,i=0,s=0,a=0,o=0;r<e.length;)(n=e.charCodeAt(r++))<128?t+=String.fromCharCode(n):(i=e.charCodeAt(r++),n>191&&n<224?(a=(31&n)<<6,a|=63&i,t+=String.fromCharCode(a)):(s=e.charCodeAt(r++),n<240?t+=String.fromCharCode((15&n)<<12|(63&i)<<6|63&s):(o=((7&n)<<18|(63&i)<<12|(63&s)<<6|63&(a=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function lc(e){var t,r,n,i=Ro(2*e.length),s=1,a=0,o=0;for(r=0;r<e.length;r+=s)s=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=64*(31&n)+(63&e.charCodeAt(r+1)),s=2):n<240?(t=4096*(15&n)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),s=3):(s=4,t=262144*(7&n)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(i[a++]=255&o,i[a++]=o>>>8,o=0),i[a++]=t%256,i[a++]=t>>>8;return i.slice(0,a).toString("ucs2")}function cc(e){return Oo(e,"binary").toString("utf8")}var hc="foo bar bazâð£",fc=Co&&(cc(hc)==oc(hc)&&cc||lc(hc)==oc(hc)&&lc)||oc,uc=Co?function(e){return Oo(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,i=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,i=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(i>>18&7))),t.push(String.fromCharCode(144+(i>>12&63))),t.push(String.fromCharCode(128+(i>>6&63))),t.push(String.fromCharCode(128+(63&i)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},dc=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}(),pc=/(^\s|\s$|\n)/;function mc(e,t){return"<"+e+(t.match(pc)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function gc(e){return Dl(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function vc(e,t,r){return"<"+e+(null!=r?gc(r):"")+(null!=t?(t.match(pc)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Tc(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}var yc="http://schemas.openxmlformats.org/package/2006/metadata/core-properties",wc="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",Ec="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",bc="http://schemas.openxmlformats.org/package/2006/content-types",Sc="http://schemas.openxmlformats.org/package/2006/relationships",Ac="http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",xc="http://purl.org/dc/elements/1.1/",Cc="http://purl.org/dc/terms/",Oc="http://purl.org/dc/dcmitype/",Rc="http://schemas.openxmlformats.org/officeDocument/2006/relationships",_c="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",Pc="http://www.w3.org/2001/XMLSchema-instance",kc="http://www.w3.org/2001/XMLSchema",Dc=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Ic="urn:schemas-microsoft-com:office:office",Nc="urn:schemas-microsoft-com:office:excel",Mc="urn:schemas-microsoft-com:office:spreadsheet",Lc="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",Fc="http://macVmlSchemaUri",Uc="urn:schemas-microsoft-com:vml",Bc="http://www.w3.org/TR/REC-html40";var Vc=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,i=e[0][r].length;n<i;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},Wc=Co?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:Oo(e)}))):Vc(e)}:Vc,jc=function(e,t,r){for(var n=[],i=t;i<r;i+=2)n.push(String.fromCharCode(lh(e,i)));return n.join("").replace(No,"")},Hc=Co?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(No,""):jc(e,t,r)}:jc,Gc=function(e,t,r){for(var n=[],i=t;i<t+r;++i)n.push(("0"+e[i].toString(16)).slice(-2));return n.join("")},$c=Co?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Gc(e,t,r)}:Gc,zc=function(e,t,r){for(var n=[],i=t;i<r;i++)n.push(String.fromCharCode(oh(e,i)));return n.join("")},Yc=Co?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):zc(e,t,r)}:zc,Xc=function(e,t){var r=hh(e,t);return r>0?Yc(e,t+4,t+4+r-1):""},Kc=Xc,Jc=function(e,t){var r=hh(e,t);return r>0?Yc(e,t+4,t+4+r-1):""},qc=Jc,Zc=function(e,t){var r=2*hh(e,t);return r>0?Yc(e,t+4,t+4+r-1):""},Qc=Zc,eh=function(e,t){var r=hh(e,t);return r>0?Hc(e,t+4,t+4+r):""},th=eh,rh=function(e,t){var r=hh(e,t);return r>0?Yc(e,t+4,t+4+r):""},nh=rh,ih=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),i=15&e[t+6],s=5;s>=0;--s)i=256*i+e[t+s];return 2047==n?0==i?r*(1/0):NaN:(0==n?n=-1022:(n-=1023,i+=Math.pow(2,52)),r*Math.pow(2,n-52)*i)}(e,t)},sh=ih,ah=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};Co&&(Kc=function(e,t){if(!Buffer.isBuffer(e))return Xc(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},qc=function(e,t){if(!Buffer.isBuffer(e))return Jc(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Qc=function(e,t){if(!Buffer.isBuffer(e))return Zc(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},th=function(e,t){if(!Buffer.isBuffer(e))return eh(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},nh=function(e,t){if(!Buffer.isBuffer(e))return rh(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},sh=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):ih(e,t)},ah=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array});var oh=function(e,t){return e[t]},lh=function(e,t){return 256*e[t+1]+e[t]},ch=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},hh=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},fh=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},uh=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function dh(e,t){var r,n,i,s,a,o,l="",c=[];switch(t){case"dbcs":if(o=this.l,Co&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(a=0;a<e;++a)l+=String.fromCharCode(lh(this,o)),o+=2;e*=2;break;case"utf8":l=Yc(this,this.l,this.l+e);break;case"utf16le":e*=2,l=Hc(this,this.l,this.l+e);break;case"wstr":return dh.call(this,e,"dbcs");case"lpstr-ansi":l=Kc(this,this.l),e=4+hh(this,this.l);break;case"lpstr-cp":l=qc(this,this.l),e=4+hh(this,this.l);break;case"lpwstr":l=Qc(this,this.l),e=4+2*hh(this,this.l);break;case"lpp4":e=4+hh(this,this.l),l=th(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+hh(this,this.l),l=nh(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,l="";0!==(i=oh(this,this.l+e++));)c.push(Eo(i));l=c.join("");break;case"_wstr":for(e=0,l="";0!==(i=lh(this,this.l+e));)c.push(Eo(i)),e+=2;e+=2,l=c.join("");break;case"dbcs-cont":for(l="",o=this.l,a=0;a<e;++a){if(this.lens&&-1!==this.lens.indexOf(o))return i=oh(this,o),this.l=o+1,s=dh.call(this,e-a,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(Eo(lh(this,o))),o+=2}l=c.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(l="",o=this.l,a=0;a!=e;++a){if(this.lens&&-1!==this.lens.indexOf(o))return i=oh(this,o),this.l=o+1,s=dh.call(this,e-a,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(Eo(oh(this,o))),o+=1}l=c.join("");break;default:switch(e){case 1:return r=oh(this,this.l),this.l++,r;case 2:return r=("i"===t?ch:lh)(this,this.l),this.l+=2,r;case 4:case-4:return"i"!==t&&128&this[this.l+3]?(n=hh(this,this.l),this.l+=4,n):(r=(e>0?fh:uh)(this,this.l),this.l+=4,r);case 8:case-8:if("f"===t)return n=8==e?sh(this,this.l):sh([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:l=$c(this,this.l,e)}}return this.l+=e,l}var ph=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},mh=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},gh=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function vh(e,t,r){var n=0,i=0;if("dbcs"===r){for(i=0;i!=t.length;++i)gh(this,t.charCodeAt(i),this.l+2*i);n=2*t.length}else if("sbcs"===r){for(t=t.replace(/[^\x00-\x7F]/g,"_"),i=0;i!=t.length;++i)this[this.l+i]=255&t.charCodeAt(i);n=t.length}else{if("hex"===r){for(;i<e;++i)this[this.l++]=parseInt(t.slice(2*i,2*i+2),16)||0;return this}if("utf16le"===r){var s=Math.min(this.l+e,this.length);for(i=0;i<Math.min(t.length,e);++i){var a=t.charCodeAt(i);this[this.l++]=255&a,this[this.l++]=a>>8}for(;this.l<s;)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,ph(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,i=0,s=0,a=n?-t:t;isFinite(a)?0==a?i=s=0:(i=Math.floor(Math.log(a)/Math.LN2),s=a*Math.pow(2,52-i),i<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?i=-1022:(s-=Math.pow(2,52),i+=1023)):(i=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&i)<<4|15&s,e[r+7]=i>>4|n}(this,t,this.l);break}case 16:break;case-4:n=4,mh(this,t,this.l)}}return this.l+=n,this}function Th(e,t){var r=$c(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function yh(e,t){e.l=t,e.read_shift=dh,e.chk=Th,e.write_shift=vh}function wh(e,t){e.l+=t}function Eh(e){var t=Ro(e);return yh(t,0),t}function bh(){var e=[],t=Co?256:2048,r=function(e){var t=Eh(e);return yh(t,0),t},n=r(t),i=function(){n&&(n.length>n.l&&((n=n.slice(0,n.l)).l=n.length),n.length>0&&e.push(n),n=null)},s=function(e){return n&&e<n.length-n.l?n:(i(),n=r(Math.max(e+1,t)))};return{next:s,push:function(e){i(),null==(n=e).l&&(n.l=n.length),s(t)},end:function(){return i(),Io(e)},_bufs:e}}function Sh(e,t,r,n){var i,s=+t;if(!isNaN(s)){n||(n=xp[s].p||(r||[]).length||0),i=1+(s>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var a=e.next(i);s<=127?a.write_shift(1,s):(a.write_shift(1,128+(127&s)),a.write_shift(1,s>>7));for(var o=0;4!=o;++o){if(!(n>=128)){a.write_shift(1,n);break}a.write_shift(1,128+(127&n)),n>>=7}n>0&&ah(r)&&e.push(r)}}function Ah(e,t,r){var n=Yl(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function xh(e,t,r){var n=Yl(e);return n.s=Ah(n.s,t.s,r),n.e=Ah(n.e,t.s,r),n}function Ch(e,t){if(e.cRel&&e.c<0)for(e=Yl(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Yl(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ih(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function Oh(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?Ch(e.s,t.biff)+":"+Ch(e.e,t.biff):(e.s.rRel?"":"$")+_h(e.s.r)+":"+(e.e.rRel?"":"$")+_h(e.e.r):(e.s.cRel?"":"$")+kh(e.s.c)+":"+(e.e.cRel?"":"$")+kh(e.e.c)}function Rh(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function _h(e){return""+(e+1)}function Ph(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function kh(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Dh(e){for(var t=0,r=0,n=0;n<e.length;++n){var i=e.charCodeAt(n);i>=48&&i<=57?t=10*t+(i-48):i>=65&&i<=90&&(r=26*r+(i-64))}return{c:r-1,r:t-1}}function Ih(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Nh(e){var t=e.indexOf(":");return-1==t?{s:Dh(e),e:Dh(e)}:{s:Dh(e.slice(0,t)),e:Dh(e.slice(t+1))}}function Mh(e,t){return void 0===t||"number"==typeof t?Mh(e.s,e.e):("string"!=typeof e&&(e=Ih(e)),"string"!=typeof t&&(t=Ih(t)),e==t?e:e+":"+t)}function Lh(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,i=0,s=e.length;for(r=0;n<s&&!((i=e.charCodeAt(n)-64)<1||i>26);++n)r=26*r+i;for(t.s.c=--r,r=0;n<s&&!((i=e.charCodeAt(n)-48)<0||i>9);++n)r=10*r+i;if(t.s.r=--r,n===s||10!=i)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=s&&!((i=e.charCodeAt(n)-64)<1||i>26);++n)r=26*r+i;for(t.e.c=--r,r=0;n!=s&&!((i=e.charCodeAt(n)-48)<0||i>9);++n)r=10*r+i;return t.e.r=--r,t}function Fh(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=Sl(e.z,r?Fl(t):t)}catch(n){}try{return e.w=Sl((e.XF||{}).numFmtId||(r?14:0),r?Fl(t):t)}catch(n){return""+t}}function Uh(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?wf[e.v]||e.v:Fh(e,null==t?e.v:t))}function Bh(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Vh(e,t,r){var n=r||{},i=e?Array.isArray(e):n.dense,s=e||(i?[]:{}),a=0,o=0;if(s&&null!=n.origin){if("number"==typeof n.origin)a=n.origin;else{var l="string"==typeof n.origin?Dh(n.origin):n.origin;a=l.r,o=l.c}s["!ref"]||(s["!ref"]="A1:A1")}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var h=Lh(s["!ref"]);c.s.c=h.s.c,c.s.r=h.s.r,c.e.c=Math.max(c.e.c,h.e.c),c.e.r=Math.max(c.e.r,h.e.r),-1==a&&(c.e.r=a=h.e.r+1)}for(var f=0;f!=t.length;++f)if(t[f]){if(!Array.isArray(t[f]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[f].length;++u)if(void 0!==t[f][u]){var d={v:t[f][u]},p=a+f,m=o+u;if(c.s.r>p&&(c.s.r=p),c.s.c>m&&(c.s.c=m),c.e.r<p&&(c.e.r=p),c.e.c<m&&(c.e.c=m),!t[f][u]||"object"!=typeof t[f][u]||Array.isArray(t[f][u])||t[f][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[f][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||$o[14],n.cellDates?(d.t="d",d.w=Sl(d.z,Fl(d.v))):(d.t="n",d.v=Fl(d.v),d.w=Sl(d.z,d.v))):d.t="s";else d=t[f][u];if(i)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=Ih({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return c.s.c<1e7&&(s["!ref"]=Mh(c)),s}function Wh(e,t){return Vh(null,e,t)}function jh(e,t){return t||(t=Eh(4)),t.write_shift(4,e),t}function Hh(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function Gh(e,t){var r=!1;return null==t&&(r=!0,t=Eh(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function $h(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function zh(e,t){var r=e.l,n=e.read_shift(1),i=Hh(e),s=[],a={t:i,h:i};if(1&n){for(var o=e.read_shift(4),l=0;l!=o;++l)s.push($h(e));a.r=s}else a.r=[{ich:0,ifnt:0}];return e.l=r+t,a}var Yh=zh;function Xh(e,t){var r=!1;return null==t&&(r=!0,t=Eh(23+4*e.t.length)),t.write_shift(1,1),Gh(e.t,t),t.write_shift(4,1),function(e,t){t||(t=Eh(4)),t.write_shift(2,0),t.write_shift(2,0)}(0,t),r?t.slice(0,t.l):t}function Kh(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Jh(e,t){return null==t&&(t=Eh(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function qh(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Zh(e,t){return null==t&&(t=Eh(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Qh=Hh,ef=Gh;function tf(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function rf(e,t){var r=!1;return null==t&&(r=!0,t=Eh(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var nf=Hh,sf=tf,af=rf;function of(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var i=0===n?sh([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):fh(t,0)>>2;return r?i/100:i}function lf(e,t){null==t&&(t=Eh(4));var r=0,n=0,i=100*e;if(e==(0|e)&&e>=-536870912&&e<1<<29?n=1:i==(0|i)&&i>=-536870912&&i<1<<29&&(n=1,r=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?i:e)<<2)+(r+2))}function cf(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var hf=cf,ff=function(e,t){return t||(t=Eh(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function uf(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function df(e,t){return(t||Eh(8)).write_shift(8,e,"f")}function pf(e,t){if(t||(t=Eh(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}var mf=80,gf={1:{n:"CodePage",t:2},2:{n:"Category",t:mf},3:{n:"PresentationFormat",t:mf},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:mf},15:{n:"Company",t:mf},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:mf},27:{n:"ContentStatus",t:mf},28:{n:"Language",t:mf},29:{n:"Version",t:mf},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},vf={1:{n:"CodePage",t:2},2:{n:"Title",t:mf},3:{n:"Subject",t:mf},4:{n:"Author",t:mf},5:{n:"Keywords",t:mf},6:{n:"Comments",t:mf},7:{n:"Template",t:mf},8:{n:"LastAuthor",t:mf},9:{n:"RevNumber",t:mf},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:mf},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}};function Tf(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var yf=Yl(Tf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),wf={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Ef={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},bf={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Sf(e,t){var r,n=function(e){for(var t=[],r=Dl(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(Ef),i=[];i[i.length]=ec,i[i.length]=vc("Types",null,{xmlns:bc,"xmlns:xsd":kc,"xmlns:xsi":Pc}),i=i.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return vc("Default",null,{Extension:e[0],ContentType:e[1]})})));var s=function(n){e[n]&&e[n].length>0&&(r=e[n][0],i[i.length]=vc("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:bf[n][t.bookType]||bf[n].xlsx}))},a=function(r){(e[r]||[]).forEach((function(e){i[i.length]=vc("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:bf[r][t.bookType]||bf[r].xlsx})}))},o=function(t){(e[t]||[]).forEach((function(e){i[i.length]=vc("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return s("workbooks"),a("sheets"),a("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),a("metadata"),o("people"),i.length>2&&(i[i.length]="</Types>",i[1]=i[1].replace("/>",">")),i.join("")}var Af={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function xf(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Cf(e){var t=[ec,vc("Relationships",null,{xmlns:Sc})];return Dl(e["!id"]).forEach((function(r){t[t.length]=vc("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Of(e,t,r,n,i,s){if(i||(i={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,i.Id="rId"+t,i.Type=n,i.Target=r,[Af.HLINK,Af.XPATH,Af.XMISS].indexOf(i.Type)>-1&&(i.TargetMode="External"),e["!id"][i.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][i.Id]=i,e[("/"+i.Target).replace("//","/")]=i,t}function Rf(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function _f(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+po.version+"</meta:generator></office:meta></office:document-meta>"}var Pf=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function kf(e,t,r,n,i){null==i[e]&&null!=t&&""!==t&&(i[e]=t,t=ic(t),n[n.length]=r?vc(e,t,r):mc(e,t))}function Df(e,t){var r=t||{},n=[ec,vc("cp:coreProperties",null,{"xmlns:cp":yc,"xmlns:dc":xc,"xmlns:dcterms":Cc,"xmlns:dcmitype":Oc,"xmlns:xsi":Pc})],i={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&kf("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:Tc(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,i),null!=e.ModifiedDate&&kf("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:Tc(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,i));for(var s=0;s!=Pf.length;++s){var a=Pf[s],o=r.Props&&null!=r.Props[a[1]]?r.Props[a[1]]:e?e[a[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&kf(a[0],o,null,n,i)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var If=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],Nf=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function Mf(e){var t=[],r=vc;return e||(e={}),e.Application="SheetJS",t[t.length]=ec,t[t.length]=vc("Properties",null,{xmlns:Ec,"xmlns:vt":_c}),If.forEach((function(n){if(void 0!==e[n[1]]){var i;switch(n[2]){case"string":i=ic(String(e[n[1]]));break;case"bool":i=e[n[1]]?"true":"false"}void 0!==i&&(t[t.length]=r(n[0],i))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+ic(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function Lf(e){var t=[ec,vc("Properties",null,{xmlns:wc,"xmlns:vt":_c})];if(!e)return t.join("");var r=1;return Dl(e).forEach((function(n){++r,t[t.length]=vc("property",function(e){switch(typeof e){case"string":var t=vc("vt:lpwstr",ic(e));return t.replace(/&quot;/g,"_x0022_");case"number":return vc((0|e)==e?"vt:i4":"vt:r8",ic(String(e)));case"boolean":return vc("vt:bool",e?"true":"false")}if(e instanceof Date)return vc("vt:filetime",Tc(e));throw new Error("Unable to serialize "+e)}(e[n]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:ic(n)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Ff={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Uf(e,t){var r=Eh(4),n=Eh(4);switch(r.write_shift(4,80==e?31:e),e){case 3:n.write_shift(-4,t);break;case 5:(n=Eh(8)).write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),n=(t-r)/Math.pow(2,32);n*=1e7;var i=(r*=1e7)/Math.pow(2,32)|0;i>0&&(r%=Math.pow(2,32),n+=i);var s=Eh(8);return s.write_shift(4,r),s.write_shift(4,n),s}(t);break;case 31:case 80:for((n=Eh(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return Io([r,n])}var Bf=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function Vf(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function Wf(e,t,r){var n=Eh(8),i=[],s=[],a=8,o=0,l=Eh(8),c=Eh(8);if(l.write_shift(4,2),l.write_shift(4,1200),c.write_shift(4,1),s.push(l),i.push(c),a+=8+l.length,!t){(c=Eh(8)).write_shift(4,0),i.unshift(c);var h=[Eh(4)];for(h[0].write_shift(4,e.length),o=0;o<e.length;++o){var f=e[o][0];for((l=Eh(8+2*(f.length+1)+(f.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,f.length+1),l.write_shift(0,f,"dbcs");l.l!=l.length;)l.write_shift(1,0);h.push(l)}l=Io(h),s.unshift(l),a+=8+l.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(Bf.indexOf(e[o][0])>-1||Nf.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}l=Uf(p.t,u)}else{var g=Vf(u);-1==g&&(g=31,u=String(u)),l=Uf(g,u)}s.push(l),(c=Eh(8)).write_shift(4,t?d:2+o),i.push(c),a+=8+l.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)i[o].write_shift(4,v),v+=s[o].length;return n.write_shift(4,a),n.write_shift(4,s.length),Io([n].concat(i).concat(s))}function jf(e,t,r,n,i,s){var a=Eh(i?68:48),o=[a];a.write_shift(2,65534),a.write_shift(2,0),a.write_shift(4,842412599),a.write_shift(16,_l.utils.consts.HEADER_CLSID,"hex"),a.write_shift(4,i?2:1),a.write_shift(16,t,"hex"),a.write_shift(4,i?68:48);var l=Wf(e,r,n);if(o.push(l),i){var c=Wf(i,null,null);a.write_shift(16,s,"hex"),a.write_shift(4,68+l.length),o.push(c)}return Io(o)}function Hf(e,t){return t||(t=Eh(2)),t.write_shift(2,+!!e),t}function Gf(e){return e.read_shift(2,"u")}function $f(e,t){return t||(t=Eh(2)),t.write_shift(2,e),t}function zf(e,t,r){return r||(r=Eh(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function Yf(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),i="sbcs-cont";(r&&r.biff,r&&8!=r.biff)?12==r.biff&&(i="wstr"):e.read_shift(1)&&(i="dbcs-cont");return r.biff>=2&&r.biff<=5&&(i="cpstr"),n?e.read_shift(n,i):""}function Xf(e){var t=e.t||"",r=Eh(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=Eh(2*t.length);return n.write_shift(2*t.length,t,"utf16le"),Io([r,n])}function Kf(e,t,r){return r||(r=Eh(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Jf(e,t){t||(t=Eh(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function qf(e){var t=Eh(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var i=n.indexOf("#"),s=i>-1?31:23;switch(n.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var a=[8,6815827,6619237,4849780,83];for(r=0;r<a.length;++r)t.write_shift(4,a[r]);if(28==s)Jf(n=n.slice(1),t);else if(2&s){for(a="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<a.length;++r)t.write_shift(1,parseInt(a[r],16));var o=i>-1?n.slice(0,i):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&Jf(i>-1?n.slice(i+1):"",t)}else{for(a="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<a.length;++r)t.write_shift(1,parseInt(a[r],16));for(var l=0;"../"==n.slice(3*l,3*l+3)||"..\\"==n.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,255&n.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Zf(e,t,r,n){return n||(n=Eh(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Qf(e,t,r){var n=r.biff>8?4:2;return[e.read_shift(n),e.read_shift(n,"i"),e.read_shift(n,"i")]}function eu(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function tu(e,t){return t||(t=Eh(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function ru(e,t,r){var n=1536,i=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":n=1280,i=8;break;case"biff4":n=4,i=6;break;case"biff3":n=3,i=6;break;case"biff2":n=2,i=4;break;default:throw new Error("unsupported BIFF version")}var s=Eh(i);return s.write_shift(2,n),s.write_shift(2,t),i>4&&s.write_shift(2,29282),i>6&&s.write_shift(2,1997),i>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function nu(e,t){var r=!t||t.biff>=8?2:1,n=Eh(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var i=n.slice(0,n.l);return i.l=n.l,i}function iu(e,t,r,n){var i=r&&5==r.biff;n||(n=Eh(i?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(i?1:2,t.length),i||n.write_shift(1,1),n.write_shift((i?1:2)*t.length,t,i?"sbcs":"utf16le");var s=n.length>n.l?n.slice(0,n.l):n;return null==s.l&&(s.l=s.length),s}function su(e,t,r,n){var i=r&&5==r.biff;n||(n=Eh(i?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&i&&(s|=1024),n.write_shift(4,s),n.write_shift(4,0),i||n.write_shift(4,0),n.write_shift(2,0),n}function au(e){var t=Eh(24),r=Dh(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),i=0;i<16;++i)t.write_shift(1,parseInt(n[i],16));return Io([t,qf(e[1])])}function ou(e){var t=e[1].Tooltip,r=Eh(10+2*(t.length+1));r.write_shift(2,2048);var n=Dh(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var i=0;i<t.length;++i)r.write_shift(2,t.charCodeAt(i));return r.write_shift(2,0),r}var lu=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Nl({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var i=Wh(function(t,r){var n=[],i=Ro(1);switch(r.type){case"base64":i=Po(xo(t));break;case"binary":i=Po(t);break;case"buffer":case"array":i=t}yh(i,0);var s=i.read_shift(1),a=!!(136&s),o=!1,l=!1;switch(s){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,a=!0;break;case 140:l=!0;break;default:throw new Error("DBF Unsupported Version: "+s.toString(16))}var c=0,h=521;2==s&&(c=i.read_shift(2)),i.l+=3,2!=s&&(c=i.read_shift(4)),c>1048576&&(c=1e6),2!=s&&(h=i.read_shift(2));var f=i.read_shift(2),u=r.codepage||1252;2!=s&&(i.l+=16,i.read_shift(1),0!==i[i.l]&&(u=e[i[i.l]]),i.l+=1,i.l+=2),l&&(i.l+=36);for(var d=[],p={},m=Math.min(i.length,2==s?521:h-10-(o?264:0)),g=l?32:11;i.l<m&&13!=i[i.l];)switch((p={}).name=wo.utils.decode(u,i.slice(i.l,i.l+g)).replace(/[\u0000\r\n].*$/g,""),i.l+=g,p.type=String.fromCharCode(i.read_shift(1)),2==s||l||(p.offset=i.read_shift(4)),p.len=i.read_shift(1),2==s&&(p.offset=i.read_shift(2)),p.dec=i.read_shift(1),p.name.length&&d.push(p),2!=s&&(i.l+=l?13:14),p.type){case"B":(!o||8!=p.len)&&r.WTF;break;case"G":case"P":r.WTF;break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+p.type)}if(13!==i[i.l]&&(i.l=h-1),13!==i.read_shift(1))throw new Error("DBF Terminator not found "+i.l+" "+i[i.l]);i.l=h;var v=0,T=0;for(n[0]=[],T=0;T!=d.length;++T)n[0][T]=d[T].name;for(;c-- >0;)if(42!==i[i.l])for(++i.l,n[++v]=[],T=0,T=0;T!=d.length;++T){var y=i.slice(i.l,i.l+d[T].len);i.l+=d[T].len,yh(y,0);var w=wo.utils.decode(u,y);switch(d[T].type){case"C":w.trim().length&&(n[v][T]=w.replace(/\s+$/,""));break;case"D":8===w.length?n[v][T]=new Date(+w.slice(0,4),+w.slice(4,6)-1,+w.slice(6,8)):n[v][T]=w;break;case"F":n[v][T]=parseFloat(w.trim());break;case"+":case"I":n[v][T]=l?2147483648^y.read_shift(-4,"i"):y.read_shift(4,"i");break;case"L":switch(w.trim().toUpperCase()){case"Y":case"T":n[v][T]=!0;break;case"N":case"F":n[v][T]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+w+"|")}break;case"M":if(!a)throw new Error("DBF Unexpected MEMO for type "+s.toString(16));n[v][T]="##MEMO##"+(l?parseInt(w.trim(),10):y.read_shift(4));break;case"N":(w=w.replace(/\u0000/g,"").trim())&&"."!=w&&(n[v][T]=+w||0);break;case"@":n[v][T]=new Date(y.read_shift(-8,"f")-621356832e5);break;case"T":n[v][T]=new Date(864e5*(y.read_shift(4)-2440588)+y.read_shift(4));break;case"Y":n[v][T]=y.read_shift(4,"i")/1e4+y.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[v][T]=-y.read_shift(-8,"f");break;case"B":if(o&&8==d[T].len){n[v][T]=y.read_shift(8,"f");break}case"G":case"P":y.l+=d[T].len;break;case"0":if("_NullFlags"===d[T].name)break;default:throw new Error("DBF Unsupported data type "+d[T].type)}}else i.l+=f;if(2!=s&&i.l<i.length&&26!=i[i.l++])throw new Error("DBF EOF Marker missing "+(i.l-1)+" of "+i.length+" "+i[i.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=d,n}(t,n),n);return i["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,i}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return Bh(r(e,t),t)}catch(n){if(t&&t.WTF)throw n}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var i=r||{};if(+i.codepage>=0&&To(+i.codepage),"string"==i.type)throw new Error("Cannot write DBF to JS string");var s=bh(),a=gm(e,{header:1,raw:!0,cellDates:!0}),o=a[0],l=a.slice(1),c=e["!cols"]||[],h=0,f=0,u=0,d=1;for(h=0;h<o.length;++h)if(((c[h]||{}).DBF||{}).name)o[h]=c[h].DBF.name,++u;else if(null!=o[h]){if(++u,"number"==typeof o[h]&&(o[h]=o[h].toString(10)),"string"!=typeof o[h])throw new Error("DBF Invalid column name "+o[h]+" |"+typeof o[h]+"|");if(o.indexOf(o[h])!==h)for(f=0;f<1024;++f)if(-1==o.indexOf(o[h]+"_"+f)){o[h]+="_"+f;break}}var p=Lh(e["!ref"]),m=[],g=[],v=[];for(h=0;h<=p.e.c-p.s.c;++h){var T="",y="",w=0,E=[];for(f=0;f<l.length;++f)null!=l[f][h]&&E.push(l[f][h]);if(0!=E.length&&null!=o[h]){for(f=0;f<E.length;++f){switch(typeof E[f]){case"number":y="B";break;case"string":default:y="C";break;case"boolean":y="L";break;case"object":y=E[f]instanceof Date?"D":"C"}w=Math.max(w,String(E[f]).length),T=T&&T!=y?"C":y}w>250&&(w=250),"C"==(y=((c[h]||{}).DBF||{}).type)&&c[h].DBF.len>w&&(w=c[h].DBF.len),"B"==T&&"N"==y&&(T="N",v[h]=c[h].DBF.dec,w=c[h].DBF.len),g[h]="C"==T||"N"==y?w:n[T]||0,d+=g[h],m[h]=T}else m[h]="?"}var b=s.next(32);for(b.write_shift(4,318902576),b.write_shift(4,l.length),b.write_shift(2,296+32*u),b.write_shift(2,d),h=0;h<4;++h)b.write_shift(4,0);for(b.write_shift(4,(+t[mo]||3)<<8),h=0,f=0;h<o.length;++h)if(null!=o[h]){var S=s.next(32),A=(o[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);S.write_shift(1,A,"sbcs"),S.write_shift(1,"?"==m[h]?"C":m[h],"sbcs"),S.write_shift(4,f),S.write_shift(1,g[h]||n[m[h]]||0),S.write_shift(1,v[h]||0),S.write_shift(1,2),S.write_shift(4,0),S.write_shift(1,0),S.write_shift(4,0),S.write_shift(4,0),f+=g[h]||n[m[h]]||0}var x=s.next(264);for(x.write_shift(4,13),h=0;h<65;++h)x.write_shift(4,0);for(h=0;h<l.length;++h){var C=s.next(d);for(C.write_shift(1,0),f=0;f<o.length;++f)if(null!=o[f])switch(m[f]){case"L":C.write_shift(1,null==l[h][f]?63:l[h][f]?84:70);break;case"B":C.write_shift(8,l[h][f]||0,"f");break;case"N":var O="0";for("number"==typeof l[h][f]&&(O=l[h][f].toFixed(v[f]||0)),u=0;u<g[f]-O.length;++u)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":l[h][f]?(C.write_shift(4,("0000"+l[h][f].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(l[h][f].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+l[h][f].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=l[h][f]?l[h][f]:"").slice(0,g[f]);for(C.write_shift(1,R,"sbcs"),u=0;u<g[f]-R.length;++u)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),cu=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+Dl(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?bo(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:bo(n)};function i(e,i){var s,a=e.split(/[\n\r]+/),o=-1,l=-1,c=0,h=0,f=[],u=[],d=null,p={},m=[],g=[],v=[],T=0;for(+i.codepage>=0&&To(+i.codepage);c!==a.length;++c){T=0;var y,w=a[c].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),E=w.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),b=E[0];if(w.length>0)switch(b){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==E[1].charAt(0)&&u.push(w.slice(3).replace(/;;/g,";"));break;case"C":var S=!1,A=!1,x=!1,C=!1,O=-1,R=-1;for(h=1;h<E.length;++h)switch(E[h].charAt(0)){case"A":case"G":break;case"X":l=parseInt(E[h].slice(1))-1,A=!0;break;case"Y":for(o=parseInt(E[h].slice(1))-1,A||(l=0),s=f.length;s<=o;++s)f[s]=[];break;case"K":'"'===(y=E[h].slice(1)).charAt(0)?y=y.slice(1,y.length-1):"TRUE"===y?y=!0:"FALSE"===y?y=!1:isNaN(Kl(y))?isNaN(ql(y).getDate())||(y=$l(y)):(y=Kl(y),null!==d&&yl(d)&&(y=Wl(y))),S=!0;break;case"E":C=!0;var _=Qu(E[h].slice(1),{r:o,c:l});f[o][l]=[f[o][l],_];break;case"S":x=!0,f[o][l]=[f[o][l],"S5S"];break;case"R":O=parseInt(E[h].slice(1))-1;break;case"C":R=parseInt(E[h].slice(1))-1;break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+w)}if(S&&(f[o][l]&&2==f[o][l].length?f[o][l][0]=y:f[o][l]=y,d=null),x){if(C)throw new Error("SYLK shared formula cannot have own formula");var P=O>-1&&f[O][R];if(!P||!P[1])throw new Error("SYLK shared formula cannot find base");f[o][l][1]=rd(P[1],{r:o-O,c:l-R})}break;case"F":var k=0;for(h=1;h<E.length;++h)switch(E[h].charAt(0)){case"X":l=parseInt(E[h].slice(1))-1,++k;break;case"Y":for(o=parseInt(E[h].slice(1))-1,s=f.length;s<=o;++s)f[s]=[];break;case"M":T=parseInt(E[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":d=u[parseInt(E[h].slice(1))];break;case"W":for(v=E[h].slice(1).split(" "),s=parseInt(v[0],10);s<=parseInt(v[1],10);++s)T=parseInt(v[2],10),g[s-1]=0===T?{hidden:!0}:{wch:T},xu(g[s-1]);break;case"C":g[l=parseInt(E[h].slice(1))-1]||(g[l]={});break;case"R":m[o=parseInt(E[h].slice(1))-1]||(m[o]={}),T>0?(m[o].hpt=T,m[o].hpx=Ru(T)):0===T&&(m[o].hidden=!0);break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+w)}k<1&&(d=null);break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+w)}}return m.length>0&&(p["!rows"]=m),g.length>0&&(p["!cols"]=g),i&&i.sheetRows&&(f=f.slice(0,i.sheetRows)),[f,p]}function s(e,t){var r=function(e,t){switch(t.type){case"base64":return i(xo(e),t);case"binary":return i(e,t);case"buffer":return i(Co&&Buffer.isBuffer(e)?e.toString("binary"):Do(e),t);case"array":return i(zl(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),n=r[0],s=r[1],a=Wh(n,t);return Dl(s).forEach((function(e){a[e]=s[e]})),a}function a(e,t,r,n){var i="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":i+=e.v||0,e.f&&!e.F&&(i+=";E"+td(e.f,{r:r,c:n}));break;case"b":i+=e.v?"TRUE":"FALSE";break;case"e":i+=e.w||e.v;break;case"d":i+='"'+(e.w||e.v)+'"';break;case"s":i+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return i}return e["|"]=254,{to_workbook:function(e,t){return Bh(s(e,t),t)},to_sheet:s,from_sheet:function(e,t){var r,n,i=["ID;PWXL;N;E"],s=[],o=Lh(e["!ref"]),l=Array.isArray(e),c="\r\n";i.push("P;PGeneral"),i.push("F;P0;DG0G8;M255"),e["!cols"]&&(n=i,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=bu(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=Su(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&n.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*Ou(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))}))}(i,e["!rows"]),i.push("B;Y"+(o.e.r-o.s.r+1)+";X"+(o.e.c-o.s.c+1)+";D"+[o.s.c,o.s.r,o.e.c,o.e.r].join(" "));for(var h=o.s.r;h<=o.e.r;++h)for(var f=o.s.c;f<=o.e.c;++f){var u=Ih({r:h,c:f});(r=l?(e[h]||[])[f]:e[u])&&(null!=r.v||r.f&&!r.F)&&s.push(a(r,0,h,f))}return i.join(c)+c+s.join(c)+c+"E"+c}}}(),hu=function(){function e(e,t){for(var r=e.split("\n"),n=-1,i=-1,s=0,a=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(n<0)){for(var o=r[s].trim().split(","),l=o[0],c=o[1],h=r[++s]||"";1&(h.match(/["]/g)||[]).length&&s<r.length-1;)h+="\n"+r[++s];switch(h=h.trim(),+l){case-1:if("BOT"===h){a[++n]=[],i=0;continue}if("EOD"!==h)throw new Error("Unrecognized DIF special command "+h);break;case 0:"TRUE"===h?a[n][i]=!0:"FALSE"===h?a[n][i]=!1:isNaN(Kl(c))?isNaN(ql(c).getDate())?a[n][i]=c:a[n][i]=$l(c):a[n][i]=Kl(c),++i;break;case 1:(h=(h=h.slice(1,h.length-1)).replace(/""/g,'"'))&&h.match(/^=".*"$/)&&(h=h.slice(2,-1)),a[n][i++]=""!==h?h:null}if("EOD"===h)break}}else a[++n]=[],i=0;return t&&t.sheetRows&&(a=a.slice(0,t.sheetRows)),a}function t(t,r){return Wh(function(t,r){switch(r.type){case"base64":return e(xo(t),r);case"binary":return e(t,r);case"buffer":return e(Co&&Buffer.isBuffer(t)?t.toString("binary"):Do(t),r);case"array":return e(zl(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return Bh(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,n,i){e.push(t),e.push(r+","+n),e.push('"'+i.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)};return function(r){var n,i=[],s=Lh(r["!ref"]),a=Array.isArray(r);e(i,"TABLE",0,1,"sheetjs"),e(i,"VECTORS",0,s.e.r-s.s.r+1,""),e(i,"TUPLES",0,s.e.c-s.s.c+1,""),e(i,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(i,-1,0,"BOT");for(var l=s.s.c;l<=s.e.c;++l){var c=Ih({r:o,c:l});if(n=a?(r[o]||[])[l]:r[c])switch(n.t){case"n":var h=n.w;h||null==n.v||(h=n.v),null==h?n.f&&!n.F?t(i,1,0,"="+n.f):t(i,1,0,""):t(i,0,h,"V");break;case"b":t(i,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(i,1,0,isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=Sl(n.z||$o[14],Fl($l(n.v)))),t(i,0,n.w,"V");break;default:t(i,1,0,"")}else t(i,1,0,"")}}t(i,-1,0,"EOD");return i.join("\r\n")}}()}}(),fu=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return Wh(function(e,t){for(var r=e.split("\n"),n=-1,i=-1,s=0,a=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var l=Dh(o[1]);if(a.length<=l.r)for(n=a.length;n<=l.r;++n)a[n]||(a[n]=[]);switch(n=l.r,i=l.c,o[2]){case"t":a[n][i]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":a[n][i]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?a[n][i]=!!+o[4]:a[n][i]=+o[4],"vtf"==o[2]&&(a[n][i]=[a[n][i],c])}}}return t&&t.sheetRows&&(a=a.slice(0,t.sheetRows)),a}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),n=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",i=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function a(t){if(!t||!t["!ref"])return"";for(var r,n=[],i=[],s="",a=Nh(t["!ref"]),o=Array.isArray(t),l=a.s.r;l<=a.e.r;++l)for(var c=a.s.c;c<=a.e.c;++c)if(s=Ih({r:l,c:c}),(r=o?(t[l]||[])[c]:t[s])&&null!=r.v&&"z"!==r.t){switch(i=["cell",s,"t"],r.t){case"s":case"str":i.push(e(r.v));break;case"n":r.f?(i[2]="vtf",i[3]="n",i[4]=r.v,i[5]=e(r.f)):(i[2]="v",i[3]=r.v);break;case"b":i[2]="vt"+(r.f?"f":"c"),i[3]="nl",i[4]=r.v?"1":"0",i[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var h=Fl($l(r.v));i[2]="vtc",i[3]="nd",i[4]=""+h,i[5]=r.w||Sl(r.z||$o[14],h);break;case"e":continue}n.push(i.join(":"))}return n.push("sheet:c:"+(a.e.c-a.s.c+1)+":r:"+(a.e.r-a.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}return{to_workbook:function(e,r){return Bh(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,n,i,n,a(e),s].join("\n")}}}(),uu=function(){function e(e,t,r,n,i){i.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(Kl(e))?isNaN(ql(e).getDate())?t[r][n]=e:t[r][n]=$l(e):t[r][n]=Kl(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var n={},i=!1,s=0,a=0;s<e.length;++s)34==(a=e.charCodeAt(s))?i=!i:!i&&a in t&&(n[a]=(n[a]||0)+1);for(s in a=[],n)Object.prototype.hasOwnProperty.call(n,s)&&a.push([n[s],s]);if(!a.length)for(s in n=r)Object.prototype.hasOwnProperty.call(n,s)&&a.push([n[s],s]);return a.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[a.pop()[1]]||44}function i(e,t){var r=t||{},i="",s=r.dense?[]:{},a={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(i=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(i=e.charAt(4),e=e.slice(6)):i=n(e.slice(0,1024)):i=r&&r.FS?r.FS:n(e.slice(0,1024));var o=0,l=0,c=0,h=0,f=0,u=i.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g,v,T=null!=r.dateNF?(g=r.dateNF,v=(v="number"==typeof g?$o[g]:g).replace(Ol,"(\\d+)"),new RegExp("^"+v+"$")):null;function y(){var t=e.slice(h,f),n={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)n.t="z";else if(r.raw)n.t="s",n.v=t;else if(0===t.trim().length)n.t="s",n.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(n.t="s",n.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(n.t="n",n.f=t.slice(1)):(n.t="s",n.v=t);else if("TRUE"==t)n.t="b",n.v=!0;else if("FALSE"==t)n.t="b",n.v=!1;else if(isNaN(c=Kl(t)))if(!isNaN(ql(t).getDate())||T&&t.match(T)){n.z=r.dateNF||$o[14];var i=0;T&&t.match(T)&&(t=function(e,t,r){var n=-1,i=-1,s=-1,a=-1,o=-1,l=-1;(t.match(Ol)||[]).forEach((function(e,t){var c=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":n=c;break;case"d":s=c;break;case"h":a=c;break;case"s":l=c;break;case"m":a>=0?o=c:i=c}})),l>=0&&-1==o&&i>=0&&(o=i,i=-1);var c=(""+(n>=0?n:(new Date).getFullYear())).slice(-4)+"-"+("00"+(i>=1?i:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==c.length&&(c="0"+c),8==c.length&&(c="20"+c);var h=("00"+(a>=0?a:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);return-1==a&&-1==o&&-1==l?c:-1==n&&-1==i&&-1==s?h:c+"T"+h}(0,r.dateNF,t.match(T)||[]),i=1),r.cellDates?(n.t="d",n.v=$l(t,i)):(n.t="n",n.v=Fl($l(t,i))),!1!==r.cellText&&(n.w=Sl(n.z,n.v instanceof Date?Fl(n.v):n.v)),r.cellNF||delete n.z}else n.t="s",n.v=t;else n.t="n",!1!==r.cellText&&(n.w=t),n.v=c;if("z"==n.t||(r.dense?(s[o]||(s[o]=[]),s[o][l]=n):s[Ih({c:l,r:o})]=n),h=f+1,m=e.charCodeAt(h),a.e.c<l&&(a.e.c=l),a.e.r<o&&(a.e.r=o),p==u)++l;else if(l=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;f<e.length;++f)switch(p=e.charCodeAt(f)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&y())break e}return f-h>0&&y(),s["!ref"]=Mh(a),s}function s(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?i(t,r):Wh(function(t,r){var n=r||{},i=[];if(!t||0===t.length)return i;for(var s=t.split(/[\r\n]/),a=s.length-1;a>=0&&0===s[a].length;)--a;for(var o=10,l=0,c=0;c<=a;++c)-1==(l=s[c].indexOf(" "))?l=s[c].length:l++,o=Math.max(o,l);for(c=0;c<=a;++c){i[c]=[];var h=0;for(e(s[c].slice(0,o).trim(),i,c,h,n),h=1;h<=(s[c].length-o)/10+1;++h)e(s[c].slice(o+10*(h-1),o+10*h).trim(),i,c,h,n)}return n.sheetRows&&(i=i.slice(0,n.sheetRows)),i}(t,r),r):i(t,r)}function a(e,t){var r="",n="string"==t.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=xo(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(e,t);switch(t.type){case"base64":r=xo(e);break;case"binary":case"string":r=e;break;case"buffer":65001==t.codepage?r=e.toString("utf8"):(t.codepage,r=Co&&Buffer.isBuffer(e)?e.toString("binary"):Do(e));break;case"array":r=zl(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=fc(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=fc(r):t.type,"socialcalc:version:"==r.slice(0,19)?fu.to_sheet("string"==t.type?r:fc(r),t):s(r,t)}return{to_workbook:function(e,t){return Bh(a(e,t),t)},to_sheet:a,from_sheet:function(e){for(var t,r=[],n=Lh(e["!ref"]),i=Array.isArray(e),s=n.s.r;s<=n.e.r;++s){for(var a=[],o=n.s.c;o<=n.e.c;++o){var l=Ih({r:s,c:o});if((t=i?(e[s]||[])[o]:e[l])&&null!=t.v){for(var c=(t.w||(Uh(t),t.w)||"").slice(0,10);c.length<10;)c+=" ";a.push(c+(0===o?" ":""))}else a.push("          ")}r.push(a.join(""))}return r.join("\n")}}}(),du=function(){function e(e,t,r){if(e){yh(e,e.l||0);for(var n=r.Enum||v;e.l<e.length;){var i=e.read_shift(2),s=n[i]||n[65535],a=e.read_shift(2),o=e.l+a,l=s.f&&s.f(e,a,r);if(e.l=o,t(l,s,i))return}}}function t(t,r){if(!t)return t;var n=r||{},i=n.dense?[]:{},s="Sheet1",a="",o=0,l={},c=[],h=[],f={s:{r:0,c:0},e:{r:0,c:0}},u=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=v,e(t,(function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:f=e;break;case 204:e&&(a=e);break;case 222:a=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&!(112&~e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||$o[14],n.cellDates&&(e[1].t="d",e[1].v=Wl(e[1].v))),n.qpro&&e[3]>o&&(i["!ref"]=Mh(f),l[s]=i,c.push(s),i=n.dense?[]:{},f={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=a||"Sheet"+(o+1),a="");var h=n.dense?(i[e[0].r]||[])[e[0].c]:i[Ih(e[0])];if(h){h.t=e[1].t,h.v=e[1].v,null!=e[1].z&&(h.z=e[1].z),null!=e[1].f&&(h.f=e[1].f);break}n.dense?(i[e[0].r]||(i[e[0].r]=[]),i[e[0].r][e[0].c]=e[1]):i[Ih(e[0])]=e[1]}}),n);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);n.Enum=T,14==t[2]&&(n.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(i["!ref"]=Mh(f),l[s]=i,c.push(s),i=n.dense?[]:{},f={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s="Sheet"+(o+1)),u>0&&e[0].r>=u)break;n.dense?(i[e[0].r]||(i[e[0].r]=[]),i[e[0].r][e[0].c]=e[1]):i[Ih(e[0])]=e[1],f.e.c<e[0].c&&(f.e.c=e[0].c),f.e.r<e[0].r&&(f.e.r=e[0].r);break;case 27:e[14e3]&&(h[e[14e3][0]]=e[14e3][1]);break;case 1537:h[e[0]]=e[1],e[0]==o&&(s=e[1])}}),n)}if(i["!ref"]=Mh(f),l[a||s]=i,c.push(a||s),!h.length)return{SheetNames:c,Sheets:l};for(var d={},p=[],m=0;m<h.length;++m)l[c[m]]?(p.push(h[m]||c[m]),d[h[m]]=l[h[m]]||l[c[m]]):(p.push(h[m]),d[h[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function r(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function n(e,t,n){var i=e.l+t,s=r(e,0,n);if(s[1].t="s",20768==n.vers){e.l++;var a=e.read_shift(1);return s[1].v=e.read_shift(a,"utf8"),s}return n.qpro&&e.l++,s[1].v=e.read_shift(i-e.l,"cstr"),s}function i(e,t,r){var n=Eh(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var i=0;i<n.length;++i){var s=r.charCodeAt(i);n.write_shift(1,s>=128?95:s)}return n.write_shift(1,0),n}function s(e,t,r){var n=Eh(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}function a(e,t,r){var n=Eh(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}function o(e,t,r){var n=32768&t;return t=(n?e:0)+((t&=-32769)>=8192?t-16384:t),(n?"":"$")+(r?kh(t):_h(t))}var l={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},c=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function f(e,t,r,n){var i=Eh(6+n.length);i.write_shift(2,e),i.write_shift(1,r),i.write_shift(1,t),i.write_shift(1,39);for(var s=0;s<n.length;++s){var a=n.charCodeAt(s);i.write_shift(1,a>=128?95:a)}return i.write_shift(1,0),i}function u(e,t){var r=h(e),n=e.read_shift(4),i=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===n&&3221225472===i?(r[1].t="e",r[1].v=15):0===n&&3489660928===i?(r[1].t="e",r[1].v=42):r[1].v=0,r;var a=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*a)*(i*Math.pow(2,s+32)+n*Math.pow(2,s)),r}function d(e,t,r,n){var i=Eh(14);if(i.write_shift(2,e),i.write_shift(1,r),i.write_shift(1,t),0==n)return i.write_shift(4,0),i.write_shift(4,0),i.write_shift(2,65535),i;var s,a=0,o=0,l=0;return n<0&&(a=1,n=-n),o=0|Math.log2(n),2147483648&(l=(n/=Math.pow(2,o-31))>>>0)||(++o,l=(n/=2)>>>0),n-=l,l|=2147483648,l>>>=0,s=(n*=Math.pow(2,32))>>>0,i.write_shift(4,s),i.write_shift(4,l),o+=16383+(a?32768:0),i.write_shift(2,o),i}function p(e,t){var r=h(e),n=e.read_shift(8,"f");return r[1].v=n,r}function m(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function g(e,t){var r=Eh(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var i=e.charCodeAt(n);r[r.l++]=i>127?95:i}return r[r.l++]=0,r}var v={0:{n:"BOF",f:Gf},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2),n):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0),n)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,n){var i=r(e,0,n);return i[1].v=e.read_shift(2,"i"),i}},14:{n:"NUMBER",f:function(e,t,n){var i=r(e,0,n);return i[1].v=e.read_shift(8,"f"),i}},15:{n:"LABEL",f:n},16:{n:"FORMULA",f:function(e,t,n){var i=e.l+t,s=r(e,0,n);if(s[1].v=e.read_shift(8,"f"),n.qpro)e.l=i;else{var a=e.read_shift(2);!function(e,t){yh(e,0);var r=[],n=0,i="",s="",a="",h="";for(;e.l<e.length;){var f=e[e.l++];switch(f){case 0:r.push(e.read_shift(8,"f"));break;case 1:s=o(t[0].c,e.read_shift(2),!0),i=o(t[0].r,e.read_shift(2),!1),r.push(s+i);break;case 2:var u=o(t[0].c,e.read_shift(2),!0),d=o(t[0].r,e.read_shift(2),!1);s=o(t[0].c,e.read_shift(2),!0),i=o(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+s+i);break;case 3:if(e.l<e.length)return;break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";f=e[e.l++];)p+=String.fromCharCode(f);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:h=r.pop(),a=r.pop(),r.push(["AND","OR"][f-20]+"("+a+","+h+")");break;default:if(f<32&&c[f])h=r.pop(),a=r.pop(),r.push(a+c[f]+h);else{if(!l[f])return;if(69==(n=l[f][1])&&(n=e[e.l++]),n>r.length)return;var m=r.slice(-n);r.length-=n,r.push(l[f][0]+"("+m.join(",")+")")}}}1==r.length&&(t[1].f=""+r[0])}(e.slice(e.l,e.l+a),s),e.l+=a}return s}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:n},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:m},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var n="";n.length<r;)n+=String.fromCharCode(e[e.l++]);return n}},65535:{n:""}},T={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=h(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:u},24:{n:"NUMBER18",f:function(e,t){var r=h(e);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=5e3*(n>>3);break;case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=u(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},n=e.l+t;e.l<n;){var i=e.read_shift(2);if(14e3==i){for(r[i]=[0,""],r[i][0]=e.read_shift(2);e[e.l];)r[i][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=h(e),n=e.read_shift(4);return r[1].v=n>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:p},40:{n:"FORMULA28",f:function(e,t){var r=p(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:m},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[n,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&To(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var n,o,l=bh(),c=Lh(e["!ref"]),h=Array.isArray(e),f=[];Cp(l,0,(n=1030,(o=Eh(2)).write_shift(2,n),o)),Cp(l,6,function(e){var t=Eh(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(c));for(var u=Math.min(c.e.r,8191),d=c.s.r;d<=u;++d)for(var p=_h(d),m=c.s.c;m<=c.e.c;++m){d===c.s.r&&(f[m]=kh(m));var g=f[m]+p,v=h?(e[d]||[])[m]:e[g];if(v&&"z"!=v.t)if("n"==v.t)(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?Cp(l,13,s(d,m,v.v)):Cp(l,14,a(d,m,v.v));else Cp(l,15,i(d,m,Uh(v).slice(0,239)))}return Cp(l,1),l.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&To(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var n=bh();Cp(n,0,function(e){var t=Eh(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,i=0,s=0;s<e.SheetNames.length;++s){var a=e.SheetNames[s],o=e.Sheets[a];if(o&&o["!ref"]){++i;var l=Nh(o["!ref"]);r<l.e.r&&(r=l.e.r),n<l.e.c&&(n=l.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,i),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var i=0,s=0;i<e.SheetNames.length;++i)(e.Sheets[e.SheetNames[i]]||{})["!ref"]&&Cp(n,27,g(e.SheetNames[i],s++));var a=0;for(i=0;i<e.SheetNames.length;++i){var o=e.Sheets[e.SheetNames[i]];if(o&&o["!ref"]){for(var l=Lh(o["!ref"]),c=Array.isArray(o),h=[],u=Math.min(l.e.r,8191),p=l.s.r;p<=u;++p)for(var m=_h(p),v=l.s.c;v<=l.e.c;++v){p===l.s.r&&(h[v]=kh(v));var T=h[v]+m,y=c?(o[p]||[])[v]:o[T];if(y&&"z"!=y.t)if("n"==y.t)Cp(n,23,d(p,v,a,y.v));else Cp(n,22,f(p,v,a,Uh(y).slice(0,239)))}++a}}return Cp(n,1),n.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(Po(xo(e)),r);case"binary":return t(Po(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),pu=/^\s|\s$|[\t\n\r]/;function mu(e,t){if(!t.bookSST)return"";var r=[ec];r[r.length]=vc("sst",null,{xmlns:Dc[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var i=e[n],s="<si>";i.r?s+=i.r:(s+="<t",i.t||(i.t=""),i.t.match(pu)&&(s+=' xml:space="preserve"'),s+=">"+ic(i.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var gu=function(e,t){var r=!1;return null==t&&(r=!0,t=Eh(15+4*e.t.length)),t.write_shift(1,0),Gh(e.t,t),r?t.slice(0,t.l):t};function vu(e){var t=bh();Sh(t,159,function(e,t){return t||(t=Eh(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)Sh(t,19,gu(e[r]));return Sh(t,160),t.end()}function Tu(e){var t,r,n=0,i=function(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}(e),s=i.length+1;for((t=Ro(s))[0]=i.length,r=1;r!=s;++r)t[r]=i[r-1];for(r=s-1;r>=0;--r)n=((16384&n?1:0)|n<<1&32767)^t[r];return 52811^n}var yu=function(){function e(e,r){switch(r.type){case"base64":return t(xo(e),r);case"binary":return t(e,r);case"buffer":return t(Co&&Buffer.isBuffer(e)?e.toString("binary"):Do(e),r);case"array":return t(zl(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw new Error("RTF missing table");var i={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var n,s=/\\\w+\b/g,a=0,o=-1;n=s.exec(e);){if("\\cell"===n[0]){var l=e.slice(a,s.lastIndex-n[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var c={v:l,t:"s"};Array.isArray(r)?r[t][o]=c:r[Ih({r:t,c:o})]=c}}a=s.lastIndex}o>i.e.c&&(i.e.c=o)})),r["!ref"]=Mh(i),r}return{to_workbook:function(t,r){return Bh(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],n=Lh(e["!ref"]),i=Array.isArray(e),s=n.s.r;s<=n.e.r;++s){r.push("\\trowd\\trautofit1");for(var a=n.s.c;a<=n.e.c;++a)r.push("\\cellx"+(a+1));for(r.push("\\pard\\intbl"),a=n.s.c;a<=n.e.c;++a){var o=Ih({r:s,c:a});(t=i?(e[s]||[])[a]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(Uh(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function wu(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Eu=6;function bu(e){return Math.floor((e+Math.round(128/Eu)/256)*Eu)}function Su(e){return Math.floor((e-5)/Eu*100+.5)/100}function Au(e){return Math.round((e*Eu+5)/Eu*256)/256}function xu(e){e.width?(e.wpx=bu(e.width),e.wch=Su(e.wpx),e.MDW=Eu):e.wpx?(e.wch=Su(e.wpx),e.width=Au(e.wch),e.MDW=Eu):"number"==typeof e.wch&&(e.width=Au(e.wch),e.wpx=bu(e.width),e.MDW=Eu),e.customWidth&&delete e.customWidth}var Cu=96;function Ou(e){return 96*e/Cu}function Ru(e){return e*Cu/96}function _u(e,t){var r,n=[ec,vc("styleSheet",null,{xmlns:Dc[0],"xmlns:vt":_c})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=e[n]&&(t[t.length]=vc("numFmt",null,{numFmtId:n,formatCode:ic(e[n])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=vc("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(n[n.length]=r),n[n.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',n[n.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',n[n.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',n[n.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=vc("cellXfs",null),e.forEach((function(e){t[t.length]=vc("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=vc("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(n[n.length]=r),n[n.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',n[n.length]='<dxfs count="0"/>',n[n.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',n.length>2&&(n[n.length]="</styleSheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Pu(e,t,r){r||(r=Eh(6+4*t.length)),r.write_shift(2,e),Gh(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}function ku(e,t){t||(t=Eh(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=Eh(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),pf(e.color,t);return t.write_shift(1,2),Gh(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Du,Iu=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Nu=wh;function Mu(e,t){t||(t=Eh(84)),Du||(Du=Nl(Iu));var r=Du[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(pf({auto:1},t),pf({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function Lu(e,t,r){r||(r=Eh(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Fu(e,t){return t||(t=Eh(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var Uu=wh;function Bu(e){var t;Sh(e,613,jh(1)),Sh(e,46,(t||(t=Eh(51)),t.write_shift(1,0),Fu(0,t),Fu(0,t),Fu(0,t),Fu(0,t),Fu(0,t),t.length>t.l?t.slice(0,t.l):t)),Sh(e,614)}function Vu(e){var t,r;Sh(e,619,jh(1)),Sh(e,48,(t={xfId:0,name:"Normal"},r||(r=Eh(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,0),r.write_shift(1,0),rf(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),Sh(e,620)}function Wu(e){Sh(e,508,function(e,t,r){var n=Eh(2052);return n.write_shift(4,e),rf(t,n),rf(r,n),n.length>n.l?n.slice(0,n.l):n}(0,"TableStyleMedium9","PivotStyleMedium4")),Sh(e,509)}function ju(e,t){var r=bh();return Sh(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r})),0!=r&&(Sh(e,615,jh(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&Sh(e,44,Pu(n,t[n]))})),Sh(e,616))}}(r,e.SSF),function(e){Sh(e,611,jh(1)),Sh(e,43,ku({sz:12,color:{theme:1},name:"Calibri",family:2})),Sh(e,612)}(r),function(e){Sh(e,603,jh(2)),Sh(e,45,Mu({patternType:"none"})),Sh(e,45,Mu({patternType:"gray125"})),Sh(e,604)}(r),Bu(r),function(e){Sh(e,626,jh(1)),Sh(e,47,Lu({numFmtId:0},65535)),Sh(e,627)}(r),function(e,t){Sh(e,617,jh(t.length)),t.forEach((function(t){Sh(e,47,Lu(t,0))})),Sh(e,618)}(r,t.cellXfs),Vu(r),function(e){Sh(e,505,jh(0)),Sh(e,506)}(r),Wu(r),Sh(r,279),r.end()}function Hu(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[ec];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Gu(){var e,t,r=bh();return Sh(r,332),Sh(r,334,jh(1)),Sh(r,335,((t=Eh(12+2*(e={name:"XLDAPR",version:12e4,flags:3496657072}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),Gh(e.name,t),t.slice(0,t.l))),Sh(r,336),Sh(r,339,function(e,t){var r=Eh(8+2*t.length);return r.write_shift(4,e),Gh(t,r),r.slice(0,r.l)}(1,"XLDAPR")),Sh(r,52),Sh(r,35,jh(514)),Sh(r,4096,jh(0)),Sh(r,4097,$f(1)),Sh(r,36),Sh(r,53),Sh(r,340),Sh(r,337,function(e){var t=Eh(8);return t.write_shift(4,e),t.write_shift(4,1),t}(1)),Sh(r,51,function(e){var t=Eh(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),Sh(r,338),Sh(r,333),r.end()}function $u(){var e=[ec];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var zu=1024;function Yu(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),i=[vc("xml",null,{"xmlns:v":Uc,"xmlns:o":Ic,"xmlns:x":Nc,"xmlns:mv":Fc}).replace(/\/>/,">"),vc("o:shapelayout",vc("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),vc("v:shapetype",[vc("v:stroke",null,{joinstyle:"miter"}),vc("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];zu<1e3*e;)zu+=1e3;return t.forEach((function(e){var t=Dh(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?vc("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=vc("v:fill",n,r);++zu,i=i.concat(["<v:shape"+gc({id:"_x0000_s"+zu,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,vc("v:shadow",null,{on:"t",obscured:"t"}),vc("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",mc("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),mc("x:AutoFill","False"),mc("x:Row",String(t.r)),mc("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),i.push("</xml>"),i.join("")}function Xu(e){var t=[ec,vc("comments",null,{xmlns:Dc[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var n=ic(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var n=0,i=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(n=r.indexOf(ic(e.a))),i.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),i.length<=1)t.push(mc("t",ic(i[0]||"")));else{for(var s="Comment:\n    "+i[0]+"\n",a=1;a<i.length;++a)s+="Reply:\n    "+i[a]+"\n";t.push(mc("t",ic(s)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Ku(e,t,r){var n=[ec,vc("ThreadedComments",null,{xmlns:Ac}).replace(/[\/]>/,">")];return e.forEach((function(e){var i="";(e[1]||[]).forEach((function(s,a){if(s.T){s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==a?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(vc("threadedComment",mc("text",s.t||""),o))}else delete s.ID}))})),n.push("</ThreadedComments>"),n.join("")}var Ju=Hh;function qu(e){var t=bh(),r=[];return Sh(t,628),Sh(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),Sh(t,632,function(e){return Gh(e.slice(0,54))}(e.a)))}))})),Sh(t,631),Sh(t,633),e.forEach((function(e){e[1].forEach((function(n){n.iauthor=r.indexOf(n.a);var i={s:Dh(e[0]),e:Dh(e[0])};Sh(t,635,function(e,t){return null==t&&(t=Eh(36)),t.write_shift(4,e[1].iauthor),ff(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([i,n])),n.t&&n.t.length>0&&Sh(t,637,Xh(n)),Sh(t,636),delete n.iauthor}))})),Sh(t,634),Sh(t,629),t.end()}var Zu=["xlsb","xlsm","xlam","biff8","xla"],Qu=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,i){var s=!1,a=!1;0==n.length?a=!0:"["==n.charAt(0)&&(a=!0,n=n.slice(1,-1)),0==i.length?s=!0:"["==i.charAt(0)&&(s=!0,i=i.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,l=i.length>0?0|parseInt(i,10):0;return s?l+=t.c:--l,a?o+=t.r:--o,r+(s?"":"$")+kh(l)+(a?"":"$")+_h(o)}return function(n,i){return t=i,n.replace(e,r)}}(),ed=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,td=function(){return function(e,t){return e.replace(ed,(function(e,r,n,i,s,a){var o=Ph(i)-(n?0:t.c),l=Rh(a)-(s?0:t.r);return r+"R"+(0==l?"":s?l+1:"["+l+"]")+"C"+(0==o?"":n?o+1:"["+o+"]")}))}}();function rd(e,t){return e.replace(ed,(function(e,r,n,i,s,a){return r+("$"==n?n+i:kh(Ph(i)+t.c))+("$"==s?s+a:_h(Rh(a)+t.r))}))}function nd(e){e.l+=1}function id(e,t){var r=e.read_shift(2);return[16383&r,r>>14&1,r>>15&1]}function sd(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return ad(e);12==r.biff&&(n=4)}var i=e.read_shift(n),s=e.read_shift(n),a=id(e),o=id(e);return{s:{r:i,c:a[0],cRel:a[1],rRel:a[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function ad(e){var t=id(e),r=id(e),n=e.read_shift(1),i=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:i,cRel:r[1],rRel:r[2]}}}function od(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=id(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var n=e.read_shift(r&&12==r.biff?4:2),i=id(e);return{r:n,c:i[0],cRel:i[1],rRel:i[2]}}function ld(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function cd(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function hd(e){return[e.read_shift(1),e.read_shift(1)]}function fd(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=function(e,t){return 1===e.read_shift(t)}(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=wf[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=uf(e);break;case 2:r[1]=function(e,t,r){if(r.biff>5)return function(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):function(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}(e,n,r)}(e,0,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function ud(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),i=[],s=0;s!=n;++s)i.push((12==r.biff?hf:eu)(e));return i}function dd(e,t,r){var n=0,i=0;12==r.biff?(n=e.read_shift(4),i=e.read_shift(4)):(i=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--i&&(i=256));for(var s=0,a=[];s!=n&&(a[s]=[]);++s)for(var o=0;o!=i;++o)a[s][o]=fd(e,r.biff);return a}function pd(e,t,r){return e.l+=2,[ld(e)]}function md(e){return e.l+=6,[]}function gd(e){return e.l+=2,[Gf(e),1&e.read_shift(2)]}var vd=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var Td={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:wh},3:{n:"PtgAdd",f:nd},4:{n:"PtgSub",f:nd},5:{n:"PtgMul",f:nd},6:{n:"PtgDiv",f:nd},7:{n:"PtgPower",f:nd},8:{n:"PtgConcat",f:nd},9:{n:"PtgLt",f:nd},10:{n:"PtgLe",f:nd},11:{n:"PtgEq",f:nd},12:{n:"PtgGe",f:nd},13:{n:"PtgGt",f:nd},14:{n:"PtgNe",f:nd},15:{n:"PtgIsect",f:nd},16:{n:"PtgUnion",f:nd},17:{n:"PtgRange",f:nd},18:{n:"PtgUplus",f:nd},19:{n:"PtgUminus",f:nd},20:{n:"PtgPercent",f:nd},21:{n:"PtgParen",f:nd},22:{n:"PtgMissArg",f:nd},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Yf(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,wf[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,uf(e)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var i=e.read_shift(r&&r.biff<=3?1:2);return[Md[i],Nd[i],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n=e[e.l++],i=e.read_shift(1),s=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[i,(0===s[0]?Nd:Id)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,i=!r||r.biff>=8?4:2,s=e.read_shift(i);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,od(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,sd(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:wh},40:{n:"PtgMemNoMem",f:wh},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var i=function(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,i=(16384&t)>>14;return t&=16383,1==n&&t>=8192&&(t-=16384),1==i&&r>=128&&(r-=256),{r:t,c:r,cRel:i,rRel:n}}(e);var i=e.read_shift(n>=12?4:2),s=e.read_shift(2),a=(16384&s)>>14,o=(32768&s)>>15;if(s&=16383,1==o)for(;i>524287;)i-=1048576;if(1==a)for(;s>8191;)s-=16384;return{r:i,c:s,cRel:a,rRel:o}}(e,0,r);return[n,i]}},45:{n:"PtgAreaN",f:function(e,t,r){var n=(96&e[e.l++])>>5,i=function(e,t,r){if(r.biff<8)return ad(e);var n=e.read_shift(12==r.biff?4:2),i=e.read_shift(12==r.biff?4:2),s=id(e),a=id(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:a[0],cRel:a[1],rRel:a[2]}}}(e,0,r);return[n,i]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var i=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,i,od(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,i=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12;break;case 12:0}return[n,i,sd(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,i=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[n,i]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,i=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[n,i]}},255:{}},yd={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},wd={1:{n:"PtgElfLel",f:gd},2:{n:"PtgElfRw",f:pd},3:{n:"PtgElfCol",f:pd},6:{n:"PtgElfRwV",f:pd},7:{n:"PtgElfColV",f:pd},10:{n:"PtgElfRadical",f:pd},11:{n:"PtgElfRadicalS",f:md},13:{n:"PtgElfColS",f:md},15:{n:"PtgElfColSV",f:md},16:{n:"PtgElfRadicalLel",f:gd},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),i=e.read_shift(2),s=e.read_shift(2);return{ixti:t,coltype:3&r,rt:vd[r>>2&31],idx:n,c:i,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Ed={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),i=[],s=0;s<=n;++s)i.push(e.read_shift(r&&2==r.biff?1:2));return i}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:cd},33:{n:"PtgAttrBaxcel",f:cd},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),hd(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),hd(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function bd(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],i=[],s=0;s<n.length;++s){var a=n[s];if(a)if(2===a[0])i.push('"'+a[1].replace(/"/g,'""')+'"');else i.push(a[1]);else i.push("")}t.push(i.join(","))}return t.join(";")}var Sd={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Ad(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var i="";if(r.biff>8)switch(e[n[0]][0]){case 357:return i=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?i:i+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return i=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?i:i+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[n[0]][0][3]?(i=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?i:i+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function xd(e,t,r){var n=Ad(e,t,r);return"#REF"==n?n:function(e,t){if(!(e||t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(n,r)}function Cd(e,t,r,n,i){var s,a,o,l,c=i&&i.biff||8,h={s:{c:0,r:0}},f=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var y=e[0][v];switch(y[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=f.pop(),a=f.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=Xl(" ",e[0][m][1][1]);break;case 1:g=Xl("\r",e[0][m][1][1]);break;default:if(g="",i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}a+=g,m=-1}f.push(a+Sd[y[0]]+s);break;case"PtgIsect":s=f.pop(),a=f.pop(),f.push(a+" "+s);break;case"PtgUnion":s=f.pop(),a=f.pop(),f.push(a+","+s);break;case"PtgRange":s=f.pop(),a=f.pop(),f.push(a+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=Ah(y[1][1],h,i),f.push(Ch(o,c));break;case"PtgRefN":o=r?Ah(y[1][1],r,i):y[1][1],f.push(Ch(o,c));break;case"PtgRef3d":u=y[1][1],o=Ah(y[1][2],h,i),p=xd(n,u,i),f.push(p+"!"+Ch(o,c));break;case"PtgFunc":case"PtgFuncVar":var w=y[1][0],E=y[1][1];w||(w=0);var b=0==(w&=127)?[]:f.slice(-w);f.length-=w,"User"===E&&(E=b.shift()),f.push(E+"("+b.join(",")+")");break;case"PtgBool":f.push(y[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":f.push(y[1]);break;case"PtgNum":f.push(String(y[1]));break;case"PtgStr":f.push('"'+y[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=xh(y[1][1],r?{s:r}:h,i),f.push(Oh(l,i));break;case"PtgArea":l=xh(y[1][1],h,i),f.push(Oh(l,i));break;case"PtgArea3d":u=y[1][1],l=y[1][2],p=xd(n,u,i),f.push(p+"!"+Oh(l,i));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgName":d=y[1][2];var S=(n.names||[])[d-1]||(n[0]||[])[d],A=S?S.Name:"SH33TJSNAME"+String(d);A&&"_xlfn."==A.slice(0,6)&&!i.xlfn&&(A=A.slice(6)),f.push(A);break;case"PtgNameX":var x,C=y[1][1];if(d=y[1][2],!(i.biff<=5)){var O="";if(14849==((n[C]||[])[0]||[])[0]||(1025==((n[C]||[])[0]||[])[0]?n[C][d]&&n[C][d].itab>0&&(O=n.SheetNames[n[C][d].itab-1]+"!"):O=n.SheetNames[d-1]+"!"),n[C]&&n[C][d])O+=n[C][d].Name;else if(n[0]&&n[0][d])O+=n[0][d].Name;else{var R=(Ad(n,C,i)||"").split(";;");R[d-1]?O=R[d-1]:O+="SH33TJSERRX"}f.push(O);break}C<0&&(C=-C),n[C]&&(x=n[C][d]),x||(x={Name:"SH33TJSERRY"}),f.push(x.Name);break;case"PtgParen":var _="(",P=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:_=Xl(" ",e[0][m][1][1])+_;break;case 3:_=Xl("\r",e[0][m][1][1])+_;break;case 4:P=Xl(" ",e[0][m][1][1])+P;break;case 5:P=Xl("\r",e[0][m][1][1])+P;break;default:if(i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}f.push(_+f.pop()+P);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":f.push("#REF!");break;case"PtgExp":o={c:y[1][1],r:y[1][0]};var k={c:r.c,r:r.r};if(n.sharedf[Ih(o)]){var D=n.sharedf[Ih(o)];f.push(Cd(D,h,k,n,i))}else{var I=!1;for(s=0;s!=n.arrayf.length;++s)if(a=n.arrayf[s],!(o.c<a[0].s.c||o.c>a[0].e.c||o.r<a[0].s.r||o.r>a[0].e.r)){f.push(Cd(a[1],h,k,n,i)),I=!0;break}I||f.push(y[1])}break;case"PtgArray":f.push("{"+bd(y[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":f.push("");break;case"PtgList":f.push("Table"+y[1].idx+"[#"+y[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(y))}if(3!=i.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var N=!0;switch((y=e[0][m])[1][0]){case 4:N=!1;case 0:g=Xl(" ",y[1][1]);break;case 5:N=!1;case 1:g=Xl("\r",y[1][1]);break;default:if(g="",i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+y[1][0])}f.push((N?g:"")+f.pop()+(N?"":g)),m=-1}}if(f.length>1&&i.WTF)throw new Error("bad formula stack");return f[0]}function Od(e,t,r,n,i){var s=Zf(t,r,i),a=function(e){if(null==e){var t=Eh(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return df("number"==typeof e?e:0)}(e.v),o=Eh(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=Eh(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];return Io([s,a,o,l])}function Rd(e,t,r){var n=e.read_shift(4),i=function(e,t,r){for(var n,i,s=e.l+t,a=[];s!=e.l;)t=s-e.l,i=e[e.l],n=Td[i]||Td[yd[i]],24!==i&&25!==i||(n=(24===i?wd:Ed)[e[e.l+1]]),n&&n.f?a.push([n.n,n.f(e,t,r)]):wh(e,t);return a}(e,n,r),s=e.read_shift(4),a=s>0?function(e,t,r,n){if(n.biff<8)return wh(e,t);for(var i=e.l+t,s=[],a=0;a!==r.length;++a)switch(r[a][0]){case"PtgArray":r[a][1]=dd(e,0,n),s.push(r[a][1]);break;case"PtgMemArea":r[a][2]=ud(e,r[a][1],n),s.push(r[a][2]);break;case"PtgExp":n&&12==n.biff&&(r[a][1][1]=e.read_shift(4),s.push(r[a][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[a][0]}return 0!==(t=i-e.l)&&s.push(wh(e,t)),s}(e,s,i,r):null;return[i,a]}var _d=Rd,Pd=Rd,kd=Rd,Dd=Rd,Id={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Nd={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Md={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};var Ld="undefined"!=typeof Map;function Fd(e,t,r){var n=0,i=e.length;if(r){if(Ld?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var s=Ld?r.get(t):r[t];n<s.length;++n)if(e[s[n]].t===t)return e.Count++,s[n]}else for(;n<i;++n)if(e[n].t===t)return e.Count++,n;return e[i]={t:t},e.Count++,e.Unique++,r&&(Ld?(r.has(t)||r.set(t,[]),r.get(t).push(i)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(i))),i}function Ud(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Eu=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=Su(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=Au(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function Bd(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function Vd(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],i=60,s=e.length;if(null==n&&r.ssf)for(;i<392;++i)if(null==r.ssf[i]){Al(t.z,i),r.ssf[i]=t.z,r.revssf[t.z]=n=i;break}for(i=0;i!=s;++i)if(e[i].numFmtId===n)return i;return e[s]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function Wd(e,t,r){if(e&&e["!ref"]){var n=Lh(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var jd=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Hd=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Gd(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var i="",s=e.t,a=e.v;if("z"!==e.t)switch(e.t){case"b":i=e.v?"1":"0";break;case"n":i=""+e.v;break;case"e":i=wf[e.v];break;case"d":n&&n.cellDates?i=$l(e.v,-1).toISOString():((e=Yl(e)).t="n",i=""+(e.v=Fl($l(e.v)))),void 0===e.z&&(e.z=$o[14]);break;default:i=e.v}var o=mc("v",ic(i)),l={r:t},c=Vd(n.cellXfs,e,n);switch(0!==c&&(l.s=c),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=mc("v",""+Fd(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str"}if(e.t!=s&&(e.t=s,e.v=a),"string"==typeof e.f&&e.f){var h=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=vc("f",ic(e.f),h)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),vc("c",o,l)}function $d(e,t,r,n){var i,s=[ec,vc("worksheet",null,{xmlns:Dc[0],"xmlns:r":Rc})],a=r.SheetNames[e],o="",l=r.Sheets[a];null==l&&(l={});var c=l["!ref"]||"A1",h=Lh(c);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw new Error("Range "+c+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),c=Mh(h)}n||(n={}),l["!comments"]=[];var f=[];!function(e,t,r,n,i){var s=!1,a={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(h){}s=!0,a.codeName=uc(ic(l))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};e["!outline"].above&&(c.summaryBelow=0),e["!outline"].left&&(c.summaryRight=0),o=(o||"")+vc("outlinePr",null,c)}(s||o)&&(i[i.length]=vc("sheetPr",o,a))}(l,r,e,t,s),s[s.length]=vc("dimension",null,{ref:c}),s[s.length]=function(e,t,r,n){var i={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(i.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),vc("sheetViews",vc("sheetView",null,i),{})}(0,0,0,r),t.sheetFormat&&(s[s.length]=vc("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=l["!cols"]&&l["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,n=["<cols>"],i=0;i!=t.length;++i)(r=t[i])&&(n[n.length]=vc("col",null,Ud(i,r)));return n[n.length]="</cols>",n.join("")}(0,l["!cols"])),s[i=s.length]="<sheetData/>",l["!links"]=[],null!=l["!ref"]&&(o=function(e,t){var r,n,i=[],s=[],a=Lh(e["!ref"]),o="",l="",c=[],h=0,f=0,u=e["!rows"],d=Array.isArray(e),p={r:l},m=-1;for(f=a.s.c;f<=a.e.c;++f)c[f]=kh(f);for(h=a.s.r;h<=a.e.r;++h){for(s=[],l=_h(h),f=a.s.c;f<=a.e.c;++f){r=c[f]+l;var g=d?(e[h]||[])[f]:e[r];void 0!==g&&null!=(o=Gd(g,r,e,t))&&s.push(o)}(s.length>0||u&&u[h])&&(p={r:l},u&&u[h]&&((n=u[h]).hidden&&(p.hidden=1),m=-1,n.hpx?m=Ou(n.hpx):n.hpt&&(m=n.hpt),m>-1&&(p.ht=m,p.customHeight=1),n.level&&(p.outlineLevel=n.level)),i[i.length]=vc("row",s.join(""),p))}if(u)for(;h<u.length;++h)u&&u[h]&&(p={r:h+1},(n=u[h]).hidden&&(p.hidden=1),m=-1,n.hpx?m=Ou(n.hpx):n.hpt&&(m=n.hpt),m>-1&&(p.ht=m,p.customHeight=1),n.level&&(p.outlineLevel=n.level),i[i.length]=vc("row","",p));return i.join("")}(l,t),o.length>0&&(s[s.length]=o)),s.length>i+1&&(s[s.length]="</sheetData>",s[i]=s[i].replace("/>",">")),l["!protect"]&&(s[s.length]=function(e){var t={sheet:1};return jd.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),Hd.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=Tu(e.password).toString(16).toUpperCase()),vc("sheetProtection",null,t)}(l["!protect"])),null!=l["!autofilter"]&&(s[s.length]=function(e,t,r,n){var i="string"==typeof e.ref?e.ref:Mh(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,a=Nh(i);a.s.r==a.e.r&&(a.e.r=Nh(t["!ref"]).e.r,i=Mh(a));for(var o=0;o<s.length;++o){var l=s[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),vc("autoFilter",null,{ref:i})}(l["!autofilter"],l,r,e)),null!=l["!merges"]&&l["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Mh(e[r])+'"/>';return t+"</mergeCells>"}(l["!merges"]));var u,d,p=-1,m=-1;return l["!links"].length>0&&(s[s.length]="<hyperlinks>",l["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=Of(n,-1,ic(e[1].Target).replace(/#.*$/,""),Af.HLINK),u["r:id"]="rId"+m),(p=e[1].Target.indexOf("#"))>-1&&(u.location=ic(e[1].Target.slice(p+1))),e[1].Tooltip&&(u.tooltip=ic(e[1].Tooltip)),s[s.length]=vc("hyperlink",null,u))})),s[s.length]="</hyperlinks>"),delete l["!links"],null!=l["!margins"]&&(s[s.length]=(Bd(d=l["!margins"]),vc("pageMargins",null,d))),t&&!t.ignoreEC&&null!=t.ignoreEC||(s[s.length]=mc("ignoredErrors",vc("ignoredError",null,{numberStoredAsText:1,sqref:c}))),f.length>0&&(m=Of(n,-1,"../drawings/drawing"+(e+1)+".xml",Af.DRAW),s[s.length]=vc("drawing",null,{"r:id":"rId"+m}),l["!drawing"]=f),l["!comments"].length>0&&(m=Of(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Af.VML),s[s.length]=vc("legacyDrawing",null,{"r:id":"rId"+m}),l["!legacy"]=m),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function zd(e,t,r,n){var i=function(e,t,r){var n=Eh(145),i=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var s=320;i.hpx?s=20*Ou(i.hpx):i.hpt&&(s=20*i.hpt),n.write_shift(2,s),n.write_shift(1,0);var a=0;i.level&&(a|=i.level),i.hidden&&(a|=16),(i.hpx||i.hpt)&&(a|=32),n.write_shift(1,a),n.write_shift(1,0);var o=0,l=n.l;n.l+=4;for(var c={r:e,c:0},h=0;h<16;++h)if(!(t.s.c>h+1<<10||t.e.c<h<<10)){for(var f=-1,u=-1,d=h<<10;d<h+1<<10;++d)c.c=d,(Array.isArray(r)?(r[c.r]||[])[c.c]:r[Ih(c)])&&(f<0&&(f=d),u=d);f<0||(++o,n.write_shift(4,f),n.write_shift(4,u))}var p=n.l;return n.l=l,n.write_shift(4,o),n.l=p,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(i.length>17||(t["!rows"]||[])[n])&&Sh(e,0,i)}var Yd=hf,Xd=ff;var Kd=hf,Jd=ff;var qd=["left","right","top","bottom","header","footer"];function Zd(e,t,r,n,i,s,a){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":(t=Yl(t)).z=t.z||$o[14],t.v=Fl($l(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v}var l={r:r,c:n};switch(l.s=Vd(i.cellXfs,t,i),t.l&&s["!links"].push([Ih(l),t.l]),t.c&&s["!comments"].push([Ih(l),t.c]),t.t){case"s":case"str":return i.bookSST?(o=Fd(i.Strings,t.v,i.revStrings),l.t="s",l.v=o,a?Sh(e,18,function(e,t,r){return null==r&&(r=Eh(8)),Zh(t,r),r.write_shift(4,t.v),r}(0,l)):Sh(e,7,function(e,t,r){return null==r&&(r=Eh(12)),Jh(t,r),r.write_shift(4,t.v),r}(0,l))):(l.t="str",a?Sh(e,17,function(e,t,r){return null==r&&(r=Eh(8+4*e.v.length)),Zh(t,r),Gh(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,l)):Sh(e,6,function(e,t,r){return null==r&&(r=Eh(12+4*e.v.length)),Jh(t,r),Gh(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,l))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?a?Sh(e,13,function(e,t,r){return null==r&&(r=Eh(8)),Zh(t,r),lf(e.v,r),r}(t,l)):Sh(e,2,function(e,t,r){return null==r&&(r=Eh(12)),Jh(t,r),lf(e.v,r),r}(t,l)):a?Sh(e,16,function(e,t,r){return null==r&&(r=Eh(12)),Zh(t,r),df(e.v,r),r}(t,l)):Sh(e,5,function(e,t,r){return null==r&&(r=Eh(16)),Jh(t,r),df(e.v,r),r}(t,l)),!0;case"b":return l.t="b",a?Sh(e,15,function(e,t,r){return null==r&&(r=Eh(5)),Zh(t,r),r.write_shift(1,e.v?1:0),r}(t,l)):Sh(e,4,function(e,t,r){return null==r&&(r=Eh(9)),Jh(t,r),r.write_shift(1,e.v?1:0),r}(t,l)),!0;case"e":return l.t="e",a?Sh(e,14,function(e,t,r){return null==r&&(r=Eh(8)),Zh(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}(t,l)):Sh(e,3,function(e,t,r){return null==r&&(r=Eh(9)),Jh(t,r),r.write_shift(1,e.v),r}(t,l)),!0}return a?Sh(e,12,function(e,t,r){return null==r&&(r=Eh(4)),Zh(t,r)}(0,l)):Sh(e,1,function(e,t,r){return null==r&&(r=Eh(8)),Jh(t,r)}(0,l)),!0}function Qd(e,t){var r,n;t&&t["!merges"]&&(Sh(e,177,(r=t["!merges"].length,null==n&&(n=Eh(4)),n.write_shift(4,r),n)),t["!merges"].forEach((function(t){Sh(e,176,Jd(t))})),Sh(e,178))}function ep(e,t){t&&t["!cols"]&&(Sh(e,390),t["!cols"].forEach((function(t,r){t&&Sh(e,60,function(e,t,r){null==r&&(r=Eh(18));var n=Ud(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0);var i=0;return t.hidden&&(i|=1),"number"==typeof n.width&&(i|=2),t.level&&(i|=t.level<<8),r.write_shift(2,i),r}(r,t))})),Sh(e,391))}function tp(e,t){var r,n;t&&t["!ref"]&&(Sh(e,648),Sh(e,649,(r=Lh(t["!ref"]),(n=Eh(24)).write_shift(4,4),n.write_shift(4,1),ff(r,n),n)),Sh(e,650))}function rp(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var n=Of(r,-1,t[1].Target.replace(/#.*$/,""),Af.HLINK);Sh(e,494,function(e,t){var r=Eh(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));ff({s:Dh(e[0]),e:Dh(e[0])},r),af("rId"+t,r);var n=e[1].Target.indexOf("#");return Gh((-1==n?"":e[1].Target.slice(n+1))||"",r),Gh(e[1].Tooltip||"",r),Gh("",r),r.slice(0,r.l)}(t,n))}})),delete t["!links"]}function np(e,t,r){Sh(e,133),Sh(e,137,function(e,t,r){null==r&&(r=Eh(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),Sh(e,138),Sh(e,134)}function ip(e,t){var r,n;t["!protect"]&&Sh(e,535,(r=t["!protect"],null==n&&(n=Eh(66)),n.write_shift(2,r.password?Tu(r.password):0),n.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?n.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):n.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),n))}function sp(e,t,r,n){var i=bh(),s=r.SheetNames[e],a=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(f){}var l,c,h=Lh(a["!ref"]||"A1");if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw new Error("Range "+(a["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575)}return a["!links"]=[],a["!comments"]=[],Sh(i,129),(r.vbaraw||a["!outline"])&&Sh(i,147,function(e,t,r){null==r&&(r=Eh(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var i=1;i<3;++i)r.write_shift(1,0);return pf({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),ef(e,r),r.slice(0,r.l)}(o,a["!outline"])),Sh(i,148,Xd(h)),np(i,0,r.Workbook),ep(i,a),function(e,t,r,n){var i,s=Lh(t["!ref"]||"A1"),a="",o=[];Sh(e,145);var l=Array.isArray(t),c=s.e.r;t["!rows"]&&(c=Math.max(s.e.r,t["!rows"].length-1));for(var h=s.s.r;h<=c;++h){a=_h(h),zd(e,t,s,h);var f=!1;if(h<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){h===s.s.r&&(o[u]=kh(u)),i=o[u]+a;var d=l?(t[h]||[])[u]:t[i];f=!!d&&Zd(e,d,h,u,n,t,f)}}Sh(e,146)}(i,a,0,t),ip(i,a),function(e,t,r,n){if(t["!autofilter"]){var i=t["!autofilter"],s="string"==typeof i.ref?i.ref:Mh(i.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var a=r.Workbook.Names,o=Nh(s);o.s.r==o.e.r&&(o.e.r=Nh(t["!ref"]).e.r,s=Mh(o));for(var l=0;l<a.length;++l){var c=a[l];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==n){c.Ref="'"+r.SheetNames[n]+"'!"+s;break}}l==a.length&&a.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+s}),Sh(e,161,ff(Lh(s))),Sh(e,162)}}(i,a,r,e),Qd(i,a),rp(i,a,n),a["!margins"]&&Sh(i,476,(l=a["!margins"],null==c&&(c=Eh(48)),Bd(l),qd.forEach((function(e){df(l[e],c)})),c)),t&&!t.ignoreEC&&null!=t.ignoreEC||tp(i,a),function(e,t,r,n){if(t["!comments"].length>0){var i=Of(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Af.VML);Sh(e,551,af("rId"+i)),t["!legacy"]=i}}(i,a,e,n),Sh(i,130),i.end()}var ap=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];var op="][*?/\\".split("");function lp(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");return op.forEach((function(t){if(-1!=e.indexOf(t))throw new Error("Sheet name cannot contain : \\ / ? * [ ]")})),!0}function cp(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,n,i=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=i,n=!!e.vbaraw,t.forEach((function(e,i){lp(e);for(var s=0;s<i;++s)if(e==t[s])throw new Error("Duplicate Sheet Name: "+e);if(n){var a=r&&r[i]&&r[i].CodeName||e;if(95==a.charCodeAt(0)&&a.length>22)throw new Error("Bad Code Name: Worksheet"+a)}}));for(var s=0;s<e.SheetNames.length;++s)Wd(e.Sheets[e.SheetNames[s]],e.SheetNames[s],s)}function hp(e){var t=[ec];t[t.length]=vc("workbook",null,{xmlns:Dc[0],"xmlns:r":Rc});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(ap.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=vc("workbookPr",null,n);var i=e.Workbook&&e.Workbook.Sheets||[],s=0;if(i&&i[0]&&i[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&i[s]&&i[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var a={name:ic(e.SheetNames[s].slice(0,31))};if(a.sheetId=""+(s+1),a["r:id"]="rId"+(s+1),i[s])switch(i[s].Hidden){case 1:a.state="hidden";break;case 2:a.state="veryHidden"}t[t.length]=vc("sheet",null,a)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=vc("definedName",ic(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function fp(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n,i=t.Workbook.Sheets,s=0,a=-1,o=-1;s<i.length;++s)!i[s]||!i[s].Hidden&&-1==a?a=s:1==i[s].Hidden&&-1==o&&(o=s);if(!(o>a))Sh(e,135),Sh(e,158,(r=a,n||(n=Eh(29)),n.write_shift(-4,0),n.write_shift(-4,460),n.write_shift(4,28800),n.write_shift(4,17600),n.write_shift(4,500),n.write_shift(4,r),n.write_shift(4,r),n.write_shift(1,120),n.length>n.l?n.slice(0,n.l):n)),Sh(e,136)}}function up(e,t){var r=bh();return Sh(r,131),Sh(r,128,function(e,t){t||(t=Eh(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return Gh("SheetJS",t),Gh(po.version,t),Gh(po.version,t),Gh("7262",t),t.length>t.l?t.slice(0,t.l):t}()),Sh(r,153,function(e,t){t||(t=Eh(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),ef(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(e.Workbook&&e.Workbook.WBProps||null)),fp(r,e),function(e,t){Sh(e,143);for(var r=0;r!=t.SheetNames.length;++r){Sh(e,156,(n={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(i=void 0)||(i=Eh(127)),i.write_shift(4,n.Hidden),i.write_shift(4,n.iTabID),af(n.strRelID,i),Gh(n.name.slice(0,31),i),i.length>i.l?i.slice(0,i.l):i))}var n,i;Sh(e,144)}(r,e),Sh(r,132),r.end()}function dp(e,t,r,n,i){return(".bin"===t.slice(-4)?sp:$d)(e,r,n,i)}function pp(e,t,r){return(".bin"===t.slice(-4)?qu:Xu)(e)}function mp(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return Dl(Ff).map((function(e){for(var t=0;t<Pf.length;++t)if(Pf[t][1]==e)return Pf[t];for(t=0;t<If.length;++t)if(If[t][1]==e)return If[t];throw e})).forEach((function(n){if(null!=e[n[1]]){var i=t&&t.Props&&null!=t.Props[n[1]]?t.Props[n[1]]:e[n[1]];"date"===n[2]&&(i=new Date(i).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof i?i=String(i):!0===i||!1===i?i=i?"1":"0":i instanceof Date&&(i=new Date(i).toISOString().replace(/\.\d*Z/,"")),r.push(mc(Ff[n[1]]||n[1],i))}})),vc("DocumentProperties",r.join(""),{xmlns:Ic})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",i=[];return e&&Dl(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var n=0;n<Pf.length;++n)if(t==Pf[n][1])return;for(n=0;n<If.length;++n)if(t==If[n][1])return;for(n=0;n<r.length;++n)if(t==r[n])return;var s=e[t],a="string";"number"==typeof s?(a="float",s=String(s)):!0===s||!1===s?(a="boolean",s=s?"1":"0"):s=String(s),i.push(vc(sc(t),s,{"dt:dt":a}))}})),t&&Dl(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var n=t[r],s="string";"number"==typeof n?(s="float",n=String(n)):!0===n||!1===n?(s="boolean",n=n?"1":"0"):n instanceof Date?(s="dateTime.tz",n=n.toISOString()):n=String(n),i.push(vc(sc(r),n,{"dt:dt":s}))}})),"<"+n+' xmlns="'+Ic+'">'+i.join("")+"</"+n+">"}(e.Props,e.Custprops)),r.join("")}function gp(e){return vc("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+td(e.Ref,{r:0,c:0})})}function vp(e,t,r,n,i,s,a){if(!e||null==e.v&&null==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+ic(td(e.f,a))),e.F&&e.F.slice(0,t.length)==t){var l=Dh(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==a.r?"":"["+(l.r-a.r)+"]")+"C"+(l.c==a.c?"":"["+(l.c-a.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=ic(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=ic(e.l.Tooltip))),r["!merges"])for(var c=r["!merges"],h=0;h!=c.length;++h)c[h].s.c==a.c&&c[h].s.r==a.r&&(c[h].e.c>c[h].s.c&&(o["ss:MergeAcross"]=c[h].e.c-c[h].s.c),c[h].e.r>c[h].s.r&&(o["ss:MergeDown"]=c[h].e.r-c[h].s.r));var f="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":f="Number",u=String(e.v);break;case"b":f="Boolean",u=e.v?"1":"0";break;case"e":f="Error",u=wf[e.v];break;case"d":f="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||$o[14]);break;case"s":f="String",u=((e.v||"")+"").replace(rc,(function(e){return tc[e]})).replace(ac,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var d=Vd(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=a.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+f+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map((function(e){var t=vc("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return vc("Comment",t,{"ss:Author":e.a})})).join("")),vc("Cell",m,o)}function Tp(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Ru(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function yp(e,t,r){var n=[],i=r.SheetNames[e],s=r.Sheets[i],a=s?function(e,t,r,n){if(!e)return"";if(!((n||{}).Workbook||{}).Names)return"";for(var i=n.Workbook.Names,s=[],a=0;a<i.length;++a){var o=i[a];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(gp(o)))}return s.join("")}(s,0,e,r):"";return a.length>0&&n.push("<Names>"+a+"</Names>"),a=s?function(e,t){if(!e["!ref"])return"";var r=Lh(e["!ref"]),n=e["!merges"]||[],i=0,s=[];e["!cols"]&&e["!cols"].forEach((function(e,t){xu(e);var r=!!e.width,n=Ud(t,e),i={"ss:Index":t+1};r&&(i["ss:Width"]=bu(n.width)),e.hidden&&(i["ss:Hidden"]="1"),s.push(vc("Column",null,i))}));for(var a=Array.isArray(e),o=r.s.r;o<=r.e.r;++o){for(var l=[Tp(o,(e["!rows"]||[])[o])],c=r.s.c;c<=r.e.c;++c){var h=!1;for(i=0;i!=n.length;++i)if(!(n[i].s.c>c||n[i].s.r>o||n[i].e.c<c||n[i].e.r<o)){n[i].s.c==c&&n[i].s.r==o||(h=!0);break}if(!h){var f={r:o,c:c},u=Ih(f),d=a?(e[o]||[])[c]:e[u];l.push(vp(d,u,e,t,0,0,f))}}l.push("</Row>"),l.length>2&&s.push(l.join(""))}return s.join("")}(s,t):"",a.length>0&&n.push("<Table>"+a+"</Table>"),n.push(function(e,t,r,n){if(!e)return"";var i=[];if(e["!margins"]&&(i.push("<PageSetup>"),e["!margins"].header&&i.push(vc("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&i.push(vc("Footer",null,{"x:Margin":e["!margins"].footer})),i.push(vc("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),i.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)i.push(vc("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!n.Workbook.Sheets[s]||n.Workbook.Sheets[s].Hidden);++s);s==r&&i.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&i.push("<DisplayRightToLeft/>"),e["!protect"]&&(i.push(mc("ProtectContents","True")),e["!protect"].objects&&i.push(mc("ProtectObjects","True")),e["!protect"].scenarios&&i.push(mc("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||i.push(mc("EnableSelection","UnlockedCells")):i.push(mc("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&i.push("<"+t[1]+"/>")}))),0==i.length?"":vc("WorksheetOptions",i.join(""),{xmlns:Nc})}(s,0,e,r)),n.join("")}function wp(e,t){t||(t={}),e.SSF||(e.SSF=Yl($o)),e.SSF&&(Cl(),xl(e.SSF),t.revssf=Ml(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Vd(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(mp(e,t)),r.push(""),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(vc("Worksheet",yp(n,t,e),{"ss:Name":ic(e.SheetNames[n])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var n=[];n.push(vc("NumberFormat",null,{"ss:Format":ic($o[e.numFmtId])}));var i={"ss:ID":"s"+(21+t)};r.push(vc("Style",n.join(""),i))})),vc("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var i=t[n];null==i.Sheet&&(i.Name.match(/^_xlfn\./)||r.push(gp(i)))}return vc("Names",r.join(""))}(e),ec+vc("Workbook",r.join(""),{xmlns:Mc,"xmlns:o":Ic,"xmlns:x":Nc,"xmlns:ss":Mc,"xmlns:dt":Lc,"xmlns:html":Bc})}var Ep="e0859ff2f94f6810ab9108002b27b3d9",bp="02d5cdd59c2e1b10939708002b2cf9ae",Sp="05d5cdd59c2e1b10939708002b2cf9ae";function Ap(e,t){var r=t||{},n=_l.utils.cfb_new({root:"R"}),i="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":i="/Workbook",r.biff=8;break;case"biff5":i="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return _l.utils.cfb_add(n,i,Mp(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],i=[],s=[],a=0,o=Il(gf,"n"),l=Il(vf,"n");if(e.Props)for(r=Dl(e.Props),a=0;a<r.length;++a)(Object.prototype.hasOwnProperty.call(o,r[a])?n:Object.prototype.hasOwnProperty.call(l,r[a])?i:s).push([r[a],e.Props[r[a]]]);if(e.Custprops)for(r=Dl(e.Custprops),a=0;a<r.length;++a)Object.prototype.hasOwnProperty.call(e.Props||{},r[a])||(Object.prototype.hasOwnProperty.call(o,r[a])?n:Object.prototype.hasOwnProperty.call(l,r[a])?i:s).push([r[a],e.Custprops[r[a]]]);var c=[];for(a=0;a<s.length;++a)Bf.indexOf(s[a][0])>-1||Nf.indexOf(s[a][0])>-1||null!=s[a][1]&&c.push(s[a]);i.length&&_l.utils.cfb_add(t,"/SummaryInformation",jf(i,Ep,l,vf)),(n.length||c.length)&&_l.utils.cfb_add(t,"/DocumentSummaryInformation",jf(n,bp,o,gf,c.length?c:null,Sp))}(e,n),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,n){if(0!=n){var i=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==i.slice(-1)&&_l.utils.cfb_add(e,i,t.FileIndex[n].content)}}))}(n,_l.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}var xp={0:{f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var i=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=i/20),r}},1:{f:function(e){return[Kh(e)]}},2:{f:function(e){return[Kh(e),of(e),"n"]}},3:{f:function(e){return[Kh(e),e.read_shift(1),"e"]}},4:{f:function(e){return[Kh(e),e.read_shift(1),"b"]}},5:{f:function(e){return[Kh(e),uf(e),"n"]}},6:{f:function(e){return[Kh(e),Hh(e),"str"]}},7:{f:function(e){return[Kh(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var n=e.l+t,i=Kh(e);i.r=r["!row"];var s=[i,Hh(e),"str"];if(r.cellFormula){e.l+=2;var a=Pd(e,n-e.l,r);s[3]=Cd(a,0,i,r.supbooks,r)}else e.l=n;return s}},9:{f:function(e,t,r){var n=e.l+t,i=Kh(e);i.r=r["!row"];var s=[i,uf(e),"n"];if(r.cellFormula){e.l+=2;var a=Pd(e,n-e.l,r);s[3]=Cd(a,0,i,r.supbooks,r)}else e.l=n;return s}},10:{f:function(e,t,r){var n=e.l+t,i=Kh(e);i.r=r["!row"];var s=[i,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var a=Pd(e,n-e.l,r);s[3]=Cd(a,0,i,r.supbooks,r)}else e.l=n;return s}},11:{f:function(e,t,r){var n=e.l+t,i=Kh(e);i.r=r["!row"];var s=[i,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var a=Pd(e,n-e.l,r);s[3]=Cd(a,0,i,r.supbooks,r)}else e.l=n;return s}},12:{f:function(e){return[qh(e)]}},13:{f:function(e){return[qh(e),of(e),"n"]}},14:{f:function(e){return[qh(e),e.read_shift(1),"e"]}},15:{f:function(e){return[qh(e),e.read_shift(1),"b"]}},16:{f:function(e){return[qh(e),uf(e),"n"]}},17:{f:function(e){return[qh(e),Hh(e),"str"]}},18:{f:function(e){return[qh(e),e.read_shift(4),"s"]}},19:{f:zh},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var i=e.read_shift(4),s=nf(e),a=kd(e,0,r),o=tf(e);e.l=n;var l={Name:s,Ptg:a};return i<268435455&&(l.Sheet=i),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var n={};n.sz=e.read_shift(2)/20;var i=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(i.fItalic&&(n.italic=1),i.fCondense&&(n.condense=1),i.fExtend&&(n.extend=1),i.fShadow&&(n.shadow=1),i.fOutline&&(n.outline=1),i.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(n.underline=s);var a=e.read_shift(1);a>0&&(n.family=a);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=function(e){var t={},r=e.read_shift(1)>>>1,n=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),a=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=n;var l=yf[n];l&&(t.rgb=wu(l));break;case 2:t.rgb=wu([s,a,o]);break;case 3:t.theme=n}return 0!=i&&(t.tint=i>0?i/32767:i/32768),t}(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=Hh(e),n}},44:{f:function(e,t){return[e.read_shift(2),Hh(e)]}},45:{f:Nu},46:{f:Uu},47:{f:function(e,t){var r=e.l+t,n=e.read_shift(2),i=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:i}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:function(e,t,r){if(!r.cellStyles)return wh(e,t);var n=r&&r.biff>=12?4:2,i=e.read_shift(n),s=e.read_shift(n),a=e.read_shift(n),o=e.read_shift(n),l=e.read_shift(2);2==n&&(e.l+=2);var c={s:i,e:s,w:a,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(c.level=l>>8&7),c}},62:{f:function(e){return[Kh(e),zh(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ih(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:wh,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=Qh(e),r}},148:{f:Yd,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var i=t>8?Hh(e):"";return i.length>0&&(r.CodeName=i),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=sf(e),r.name=Hh(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:hf},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Kd},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Hh(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:sf},357:{},358:{},359:{},360:{T:1},361:{},362:{f:function(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var n=Yf(e,0,r);return 3==n.charCodeAt(0)?n.slice(1):n}(e,0,r);for(var n=[],i=e.l+t,s=e.read_shift(r.biff>8?4:2);0!==s--;)n.push(Qf(e,r.biff,r));if(e.l!=i)throw new Error("Bad ExternSheet: "+e.l+" != "+i);return n}},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var n=e.l+t,i=cf(e),s=e.read_shift(1),a=[i];if(a[2]=s,r.cellFormula){var o=_d(e,n-e.l,r);a[1]=o}else e.l=n;return a}},427:{f:function(e,t,r){var n=e.l+t,i=[hf(e)];if(r.cellFormula){var s=Dd(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return qd.forEach((function(r){t[r]=uf(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,n=hf(e),i=tf(e),s=Hh(e),a=Hh(e),o=Hh(e);e.l=r;var l={rfx:n,relId:i,loc:s,display:o};return a&&(l.Tooltip=a),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:sf},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Ju},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=hf(e);return t.rfx=r.s,t.ref=Ih(r.s),e.l+=16,t}},636:{T:-1},637:{f:Yh},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:Hh(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function Cp(e,t,r,n){var i=t;if(!isNaN(i)){var s=n||(r||[]).length||0,a=e.next(4);a.write_shift(2,i),a.write_shift(2,s),s>0&&ah(r)&&e.push(r)}}function Op(e,t,r){return e||(e=Eh(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Rp(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var i="d"==t.t?Fl($l(t.v)):t.v;return void(i==(0|i)&&i>=0&&i<65536?Cp(e,2,(s=r,a=n,o=i,l=Eh(9),Op(l,s,a),l.write_shift(2,o),l)):Cp(e,3,function(e,t,r){var n=Eh(15);return Op(n,e,t),n.write_shift(8,r,"f"),n}(r,n,i)));case"b":case"e":return void Cp(e,5,function(e,t,r,n){var i=Eh(9);return Op(i,e,t),zf(r,n||"b",i),i}(r,n,t.v,t.t));case"s":case"str":return void Cp(e,4,function(e,t,r){var n=Eh(8+2*r.length);return Op(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}(r,n,(t.v||"").slice(0,255)))}var s,a,o,l;Cp(e,1,Op(null,r,n))}function _p(e,t){for(var r=t||{},n=bh(),i=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(i=s);if(0==i&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return Cp(n,4==r.biff?1033:3==r.biff?521:9,ru(0,16,r)),function(e,t,r,n){var i,s=Array.isArray(t),a=Lh(t["!ref"]||"A1"),o="",l=[];if(a.e.c>255||a.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");a.e.c=Math.min(a.e.c,255),a.e.r=Math.min(a.e.c,16383),i=Mh(a)}for(var c=a.s.r;c<=a.e.r;++c){o=_h(c);for(var h=a.s.c;h<=a.e.c;++h){c===a.s.r&&(l[h]=kh(h)),i=l[h]+o;var f=s?(t[c]||[])[h]:t[i];f&&Rp(e,f,c,h)}}}(n,e.Sheets[e.SheetNames[i]],0,r),Cp(n,10),n.end()}function Pp(e,t,r){Cp(e,49,function(e,t){var r=e.name||"Arial",n=t&&5==t.biff,i=Eh(n?15+r.length:16+2*r.length);return i.write_shift(2,20*e.sz),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}({sz:12,name:"Arial"},r))}function kp(e,t){if(t){var r=0;t.forEach((function(t,n){++r<=256&&t&&Cp(e,125,function(e,t){var r=Eh(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}(Ud(n,t),n))}))}}function Dp(e,t,r,n,i){var s=16+Vd(i.cellXfs,t,i);if(null!=t.v||t.bf)if(t.bf)Cp(e,6,Od(t,r,n,0,s));else switch(t.t){case"d":case"n":Cp(e,515,function(e,t,r,n){var i=Eh(14);return Zf(e,t,n,i),df(r,i),i}(r,n,"d"==t.t?Fl($l(t.v)):t.v,s));break;case"b":case"e":Cp(e,517,function(e,t,r,n,i,s){var a=Eh(8);return Zf(e,t,n,a),zf(r,s,a),a}(r,n,t.v,s,0,t.t));break;case"s":case"str":if(i.bookSST)Cp(e,253,function(e,t,r,n){var i=Eh(10);return Zf(e,t,n,i),i.write_shift(4,r),i}(r,n,Fd(i.Strings,t.v,i.revStrings),s));else Cp(e,516,function(e,t,r,n,i){var s=!i||8==i.biff,a=Eh(+s+8+(1+s)*r.length);return Zf(e,t,n,a),a.write_shift(2,r.length),s&&a.write_shift(1,1),a.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),a}(r,n,(t.v||"").slice(0,255),s,i));break;default:Cp(e,513,Zf(r,n,s))}else Cp(e,513,Zf(r,n,s))}function Ip(e,t,r){var n,i,s=bh(),a=r.SheetNames[e],o=r.Sheets[a]||{},l=(r||{}).Workbook||{},c=(l.Sheets||[])[e]||{},h=Array.isArray(o),f=8==t.biff,u="",d=[],p=Lh(o["!ref"]||"A1"),m=f?65536:16384;if(p.e.c>255||p.e.r>=m){if(t.WTF)throw new Error("Range "+(o["!ref"]||"A1")+" exceeds format limit A1:IV16384");p.e.c=Math.min(p.e.c,255),p.e.r=Math.min(p.e.c,m-1)}Cp(s,2057,ru(0,16,t)),Cp(s,13,$f(1)),Cp(s,12,$f(100)),Cp(s,15,Hf(!0)),Cp(s,17,Hf(!1)),Cp(s,16,df(.001)),Cp(s,95,Hf(!0)),Cp(s,42,Hf(!1)),Cp(s,43,Hf(!1)),Cp(s,130,$f(1)),Cp(s,128,((i=Eh(8)).write_shift(4,0),i.write_shift(2,0),i.write_shift(2,0),i)),Cp(s,131,Hf(!1)),Cp(s,132,Hf(!1)),f&&kp(s,o["!cols"]),Cp(s,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,n=Eh(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}(p,t)),f&&(o["!links"]=[]);for(var g=p.s.r;g<=p.e.r;++g){u=_h(g);for(var v=p.s.c;v<=p.e.c;++v){g===p.s.r&&(d[v]=kh(v)),n=d[v]+u;var T=h?(o[g]||[])[v]:o[n];T&&(Dp(s,T,g,v,t),f&&T.l&&o["!links"].push([n,T.l]))}}var y=c.CodeName||c.name||a;return f&&Cp(s,574,function(e){var t=Eh(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((l.Views||[])[0])),f&&(o["!merges"]||[]).length&&Cp(s,229,function(e){var t=Eh(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)tu(e[r],t);return t}(o["!merges"])),f&&function(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];Cp(e,440,au(n)),n[1].Tooltip&&Cp(e,2048,ou(n))}delete t["!links"]}(s,o),Cp(s,442,Kf(y)),f&&function(e,t){var r=Eh(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),Cp(e,2151,r),(r=Eh(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),tu(Lh(t["!ref"]||"A1"),r),r.write_shift(4,4),Cp(e,2152,r)}(s,o),Cp(s,10),s.end()}function Np(e,t,r){var n,i=bh(),s=(e||{}).Workbook||{},a=s.Sheets||[],o=s.WBProps||{},l=8==r.biff,c=5==r.biff;(Cp(i,2057,ru(0,5,r)),"xla"==r.bookType&&Cp(i,135),Cp(i,225,l?$f(1200):null),Cp(i,193,function(e,t){t||(t=Eh(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),c&&Cp(i,191),c&&Cp(i,192),Cp(i,226),Cp(i,92,function(e,t){var r=!t||8==t.biff,n=Eh(r?112:54);for(n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}(0,r)),Cp(i,66,$f(l?1200:1252)),l&&Cp(i,353,$f(0)),l&&Cp(i,448),Cp(i,317,function(e){for(var t=Eh(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),l&&e.vbaraw&&Cp(i,211),l&&e.vbaraw)&&Cp(i,442,Kf(o.CodeName||"ThisWorkbook"));Cp(i,156,$f(17)),Cp(i,25,Hf(!1)),Cp(i,18,Hf(!1)),Cp(i,19,$f(0)),l&&Cp(i,431,Hf(!1)),l&&Cp(i,444,$f(0)),Cp(i,61,((n=Eh(18)).write_shift(2,0),n.write_shift(2,0),n.write_shift(2,29280),n.write_shift(2,17600),n.write_shift(2,56),n.write_shift(2,0),n.write_shift(2,0),n.write_shift(2,1),n.write_shift(2,500),n)),Cp(i,64,Hf(!1)),Cp(i,141,$f(0)),Cp(i,34,Hf("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&function(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}(e.Workbook.WBProps.date1904)?"true":"false"}(e))),Cp(i,14,Hf(!0)),l&&Cp(i,439,Hf(!1)),Cp(i,218,$f(0)),Pp(i,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(n){for(var i=n[0];i<=n[1];++i)null!=t[i]&&Cp(e,1054,iu(i,t[i],r))}))}(i,e.SSF,r),function(e,t){for(var r=0;r<16;++r)Cp(e,224,su({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){Cp(e,224,su(r,0,t))}))}(i,r),l&&Cp(i,352,Hf(!1));var h=i.end(),f=bh();l&&Cp(f,140,function(e){return e||(e=Eh(4)),e.write_shift(2,1),e.write_shift(2,1),e}()),l&&r.Strings&&function(e,t,r){var n=(r||[]).length||0;if(n<=8224)return Cp(e,t,r,n);var i=t;if(!isNaN(i)){for(var s=r.parts||[],a=0,o=0,l=0;l+(s[a]||8224)<=8224;)l+=s[a]||8224,a++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<n;){for((c=e.next(4)).write_shift(2,60),l=0;l+(s[a]||8224)<=8224;)l+=s[a]||8224,a++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}(f,252,function(e){var t=Eh(8);t.write_shift(4,e.Count),t.write_shift(4,e.Unique);for(var r=[],n=0;n<e.length;++n)r[n]=Xf(e[n]);var i=Io([t].concat(r));return i.parts=[t.length].concat(r.map((function(e){return e.length}))),i}(r.Strings)),Cp(f,10);var u=f.end(),d=bh(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(l?12:11)+(l?2:1)*e.SheetNames[m].length;var g=h.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){Cp(d,133,nu({pos:g,hs:(a[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var v=d.end();if(p!=v.length)throw new Error("BS8 "+p+" != "+v.length);var T=[];return h.length&&T.push(h),v.length&&T.push(v),u.length&&T.push(u),Io(T)}function Mp(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(n&&n["!ref"])Nh(n["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error}var i=t||{};switch(i.biff||2){case 8:case 5:return function(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=Yl($o)),e&&e.SSF&&(Cl(),xl(e.SSF),r.revssf=Ml(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,om(r),r.cellXfs=[],Vd(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var i=0;i<e.SheetNames.length;++i)n[n.length]=Ip(i,r,e);return n.unshift(Np(e,n,r)),Io(n)}(e,t);case 4:case 3:case 2:return _p(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Lp(e,t,r,n){for(var i=e["!merges"]||[],s=[],a=t.s.c;a<=t.e.c;++a){for(var o=0,l=0,c=0;c<i.length;++c)if(!(i[c].s.r>r||i[c].s.c>a||i[c].e.r<r||i[c].e.c<a)){if(i[c].s.r<r||i[c].s.c<a){o=-1;break}o=i[c].e.r-i[c].s.r+1,l=i[c].e.c-i[c].s.c+1;break}if(!(o<0)){var h=Ih({r:r,c:a}),f=n.dense?(e[r]||[])[a]:e[h],u=f&&null!=f.v&&(f.h||((f.w||(Uh(f),f.w)||"")+"").replace(rc,(function(e){return tc[e]})).replace(/\n/g,"<br/>").replace(ac,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":f&&(d["data-t"]=f&&f.t||"z",null!=f.v&&(d["data-v"]=f.v),null!=f.z&&(d["data-z"]=f.z),f.l&&"#"!=(f.l.Target||"#").charAt(0)&&(u='<a href="'+f.l.Target+'">'+u+"</a>")),d.id=(n.id||"sjs")+"-"+h,s.push(vc("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}function Fp(e,t){var r=t||{},n=null!=r.header?r.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',i=null!=r.footer?r.footer:"</body></html>",s=[n],a=Nh(e["!ref"]);r.dense=Array.isArray(e),s.push(function(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}(0,0,r));for(var o=a.s.r;o<=a.e.r;++o)s.push(Lp(e,a,o,r));return s.push("</table>"+i),s.join("")}function Up(e,t,r){var n=r||{},i=0,s=0;if(null!=n.origin)if("number"==typeof n.origin)i=n.origin;else{var a="string"==typeof n.origin?Dh(n.origin):n.origin;i=a.r,s=a.c}var o=t.getElementsByTagName("tr"),l=Math.min(n.sheetRows||1e7,o.length),c={s:{r:0,c:0},e:{r:i,c:s}};if(e["!ref"]){var h=Nh(e["!ref"]);c.s.r=Math.min(c.s.r,h.s.r),c.s.c=Math.min(c.s.c,h.s.c),c.e.r=Math.max(c.e.r,h.e.r),c.e.c=Math.max(c.e.c,h.e.c),-1==i&&(c.e.r=i=h.e.r+1)}var f=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,T=0,y=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<l;++p){var w=o[p];if(Vp(w)){if(n.display)continue;d[m]={hidden:!0}}var E=w.children;for(g=v=0;g<E.length;++g){var b=E[g];if(!n.display||!Vp(b)){var S=b.hasAttribute("data-v")?b.getAttribute("data-v"):b.hasAttribute("v")?b.getAttribute("v"):dc(b.innerHTML),A=b.getAttribute("data-z")||b.getAttribute("z");for(u=0;u<f.length;++u){var x=f[u];x.s.c==v+s&&x.s.r<m+i&&m+i<=x.e.r&&(v=x.e.c+1-s,u=-1)}y=+b.getAttribute("colspan")||1,((T=+b.getAttribute("rowspan")||1)>1||y>1)&&f.push({s:{r:m+i,c:v+s},e:{r:m+i+(T||1)-1,c:v+s+(y||1)-1}});var C={t:"s",v:S},O=b.getAttribute("data-t")||b.getAttribute("t")||"";null!=S&&(0==S.length?C.t=O||"z":n.raw||0==S.trim().length||"s"==O||("TRUE"===S?C={t:"b",v:!0}:"FALSE"===S?C={t:"b",v:!1}:isNaN(Kl(S))?isNaN(ql(S).getDate())||(C={t:"d",v:$l(S)},n.cellDates||(C={t:"n",v:Fl(C.v)}),C.z=n.dateNF||$o[14]):C={t:"n",v:Kl(S)})),void 0===C.z&&null!=A&&(C.z=A);var R="",_=b.getElementsByTagName("A");if(_&&_.length)for(var P=0;P<_.length&&(!_[P].hasAttribute("href")||"#"==(R=_[P].getAttribute("href")).charAt(0));++P);R&&"#"!=R.charAt(0)&&(C.l={Target:R}),n.dense?(e[m+i]||(e[m+i]=[]),e[m+i][v+s]=C):e[Ih({c:v+s,r:m+i})]=C,c.e.c<v+s&&(c.e.c=v+s),v+=y}}++m}return f.length&&(e["!merges"]=(e["!merges"]||[]).concat(f)),c.e.r=Math.max(c.e.r,m-1+i),e["!ref"]=Mh(c),m>=l&&(e["!fullref"]=Mh((c.e.r=o.length-p+m-1+i,c))),e}function Bp(e,t){return Up((t||{}).dense?[]:{},e,t)}function Vp(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}var Wp=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+gc({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return ec+t}}(),jp=function(){var e="          <table:table-cell />\n",t=function(t,r,n){var i=[];i.push('      <table:table table:name="'+ic(r.SheetNames[n])+'" table:style-name="ta1">\n');var s=0,a=0,o=Nh(t["!ref"]||"A1"),l=t["!merges"]||[],c=0,h=Array.isArray(t);if(t["!cols"])for(a=0;a<=o.e.c;++a)i.push("        <table:table-column"+(t["!cols"][a]?' table:style-name="co'+t["!cols"][a].ods+'"':"")+"></table:table-column>\n");var f="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)f=u[s]?' table:style-name="ro'+u[s].ods+'"':"",i.push("        <table:table-row"+f+"></table:table-row>\n");for(;s<=o.e.r;++s){for(f=u[s]?' table:style-name="ro'+u[s].ods+'"':"",i.push("        <table:table-row"+f+">\n"),a=0;a<o.s.c;++a)i.push(e);for(;a<=o.e.c;++a){var d=!1,p={},m="";for(c=0;c!=l.length;++c)if(!(l[c].s.c>a||l[c].s.r>s||l[c].e.c<a||l[c].e.r<s)){l[c].s.c==a&&l[c].s.r==s||(d=!0),p["table:number-columns-spanned"]=l[c].e.c-l[c].s.c+1,p["table:number-rows-spanned"]=l[c].e.r-l[c].s.r+1;break}if(d)i.push("          <table:covered-table-cell/>\n");else{var g=Ih({r:s,c:a}),v=h?(t[s]||[])[a]:t[g];if(v&&v.f&&(p["table:formula"]=ic(("of:="+v.f.replace(ed,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var T=Nh(v.F);p["table:number-matrix-columns-spanned"]=T.e.c-T.s.c+1,p["table:number-matrix-rows-spanned"]=T.e.r-T.s.r+1}if(v){switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||$l(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=$l(v.v).toISOString(),p["table:style-name"]="ce1";break;default:i.push(e);continue}var y=ic(m).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var w=v.l.Target;"#"==(w="#"==w.charAt(0)?"#"+w.slice(1).replace(/\./,"!"):w).charAt(0)||w.match(/^\w+:/)||(w="../"+w),y=vc("text:a",y,{"xlink:href":w.replace(/&/g,"&amp;")})}i.push("          "+vc("table:table-cell",vc("text:p",y,{}),p)+"\n")}else i.push(e)}}i.push("        </table:table-row>\n")}return i.push("      </table:table>\n"),i.join("")};return function(e,r){var n=[ec],i=gc({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=gc({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(n.push("<office:document"+i+s+">\n"),n.push(_f().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+i+">\n"),function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var i=t["!cols"][n];if(null==i.width&&null==i.wpx&&null==i.wch)continue;xu(i),i.ods=r;var s=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}));var n=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var i=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+i+'"/>\n'),e.push("  </style:style>\n"),++n}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")}(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var a=0;a!=e.SheetNames.length;++a)n.push(t(e.Sheets[e.SheetNames[a]],e,a));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==r.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function Hp(e,t){if("fods"==t.bookType)return jp(e,t);var r=Ql(),n="",i=[],s=[];return Zl(r,n="mimetype","application/vnd.oasis.opendocument.spreadsheet"),Zl(r,n="content.xml",jp(e,t)),i.push([n,"text/xml"]),s.push([n,"ContentFile"]),Zl(r,n="styles.xml",Wp(e,t)),i.push([n,"text/xml"]),s.push([n,"StylesFile"]),Zl(r,n="meta.xml",ec+_f()),i.push([n,"text/xml"]),s.push([n,"MetadataFile"]),Zl(r,n="manifest.rdf",function(e){var t,r,n=[ec];n.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var i=0;i!=e.length;++i)n.push(Rf(e[i][0],e[i][1])),n.push((t="",r=e[i][0],['  <rdf:Description rdf:about="'+t+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")));return n.push(Rf("","Document","pkg")),n.push("</rdf:RDF>"),n.join("")}(s)),i.push([n,"application/rdf+xml"]),Zl(r,n="META-INF/manifest.xml",function(e){var t=[ec];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(i)),r}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Gp(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function $p(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):Po(uc(e))}function zp(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),n=0;return e.forEach((function(e){r.set(e,n),n+=e.length})),r}function Yp(e,t){var r=t?t[0]:0,n=127&e[r];e:if(e[r++]>=128){if(n|=(127&e[r])<<7,e[r++]<128)break e;if(n|=(127&e[r])<<14,e[r++]<128)break e;if(n|=(127&e[r])<<21,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),n}function Xp(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Kp(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function Jp(e){for(var t=[],r=[0];r[0]<e.length;){var n,i=r[0],s=Yp(e,r),a=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(a){case 0:for(var l=r[0];e[r[0]++]>=128;);n=e.slice(l,r[0]);break;case 5:o=4,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Yp(e,r),n=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw new Error("PB Type ".concat(a," for Field ").concat(s," at offset ").concat(i))}var c={data:n,type:a};null==t[s]?t[s]=[c]:t[s].push(c)}return t}function qp(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Xp(8*r+e.type)),2==e.type&&t.push(Xp(e.data.length)),t.push(e.data))}))})),zp(t)}function Zp(e){for(var t,r=[],n=[0];n[0]<e.length;){var i=Yp(e,n),s=Jp(e.slice(n[0],n[0]+i));n[0]+=i;var a={id:Kp(s[1][0].data),messages:[]};s[2].forEach((function(t){var r=Jp(t.data),i=Kp(r[3][0].data);a.messages.push({meta:r,data:e.slice(n[0],n[0]+i)}),n[0]+=i})),(null==(t=s[3])?void 0:t[0])&&(a.merge=Kp(s[3][0].data)>>>0>0),r.push(a)}return r}function Qp(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Xp(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Xp(+!!e.merge),type:0}]);var n=[];e.messages.forEach((function(e){n.push(e.data),e.meta[3]=[{type:0,data:Xp(e.data.length)}],r[2].push({data:qp(e.meta),type:2})}));var i=qp(r);t.push(Xp(i.length)),t.push(i),n.forEach((function(e){return t.push(e)}))})),zp(t)}function em(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Yp(t,r),i=[];r[0]<t.length;){var s=3&t[r[0]];if(0!=s){var a=0,o=0;if(1==s?(o=4+(t[r[0]]>>2&7),a=(224&t[r[0]++])<<3,a|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==s?(a=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(a=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),i=[zp(i)],0==a)throw new Error("Invalid offset 0");if(a>i[0].length)throw new Error("Invalid offset beyond length");if(o>=a)for(i.push(i[0].slice(-a)),o-=a;o>=i[i.length-1].length;)i.push(i[i.length-1]),o-=i[i.length-1].length;i.push(i[0].slice(-a,-a+o))}else{var l=t[r[0]++]>>2;if(l<60)++l;else{var c=l-59;l=t[r[0]],c>1&&(l|=t[r[0]+1]<<8),c>2&&(l|=t[r[0]+2]<<16),c>3&&(l|=t[r[0]+3]<<24),l>>>=0,l++,r[0]+=c}i.push(t.slice(r[0],r[0]+l)),r[0]+=l}}var h=zp(i);if(h.length!=n)throw new Error("Unexpected length: ".concat(h.length," != ").concat(n));return h}function tm(e){for(var t=[],r=0;r<e.length;){var n=e[r++],i=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(em(n,e.slice(r,r+i))),r+=i}if(r!==e.length)throw new Error("data is not a valid framed stream!");return zp(t)}function rm(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),i=new Uint8Array(4);t.push(i);var s=Xp(n),a=s.length;t.push(s),n<=60?(a++,t.push(new Uint8Array([n-1<<2]))):n<=256?(a+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(a+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(a+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(a+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),a+=n,i[0]=0,i[1]=255&a,i[2]=a>>8&255,i[3]=a>>16&255,r+=n}return zp(t)}function nm(e,t){var r=new Uint8Array(32),n=Gp(r),i=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,i=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var s=0;i>=1;++s,i/=256)e[t+s]=255&i;e[t+15]|=r>=0?0:128}(r,i,e.v),s|=1,i+=16;break;case"b":r[1]=6,n.setFloat64(i,e.v?1:0,!0),s|=2,i+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(i,t.indexOf(e.v),!0),s|=8,i+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,s,!0),r.slice(0,i)}function im(e,t){var r=new Uint8Array(32),n=Gp(r),i=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(i,e.v,!0),s|=32,i+=8;break;case"b":r[2]=6,n.setFloat64(i,e.v?1:0,!0),s|=32,i+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(i,t.indexOf(e.v),!0),s|=16,i+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,s,!0),r.slice(0,i)}function sm(e){return Yp(Jp(e)[1][0].data)}function am(e,t,r){var n,i,s,a;if(!(null==(n=e[6])?void 0:n[0])||!(null==(i=e[7])?void 0:i[0]))throw"Mutation only works on post-BNC storages!";if((null==(a=null==(s=e[8])?void 0:s[0])?void 0:a.data)&&Kp(e[8][0].data)>0||!1)throw"Math only works with normal offsets";for(var o=0,l=Gp(e[7][0].data),c=0,h=[],f=Gp(e[4][0].data),u=0,d=[],p=0;p<t.length;++p)if(null!=t[p]){var m,g;switch(l.setUint16(2*p,c,!0),f.setUint16(2*p,u,!0),typeof t[p]){case"string":m=nm({t:"s",v:t[p]},r),g=im({t:"s",v:t[p]},r);break;case"number":m=nm({t:"n",v:t[p]},r),g=im({t:"n",v:t[p]},r);break;case"boolean":m=nm({t:"b",v:t[p]},r),g=im({t:"b",v:t[p]},r);break;default:throw new Error("Unsupported value "+t[p])}h.push(m),c+=m.length,d.push(g),u+=g.length,++o}else l.setUint16(2*p,65535,!0),f.setUint16(2*p,65535);for(e[2][0].data=Xp(o);p<e[7][0].data.length/2;++p)l.setUint16(2*p,65535,!0),f.setUint16(2*p,65535,!0);return e[6][0].data=zp(h),e[3][0].data=zp(d),o}function om(e){var t;(t=[["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]],function(e){for(var r=0;r!=t.length;++r){var n=t[r];void 0===e[n[0]]&&(e[n[0]]=n[1]),"n"===n[2]&&(e[n[0]]=Number(e[n[0]]))}})(e)}function lm(e,t){return"ods"==t.bookType?Hp(e,t):"numbers"==t.bookType?function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length;var n=Nh(r["!ref"]);n.s.r=n.s.c=0,n.e.c>9&&(n.e.c=9),n.e.r>49&&(n.e.r=49);var i=gm(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&s.push(e)}))}));var a={},o=[],l=_l.read(t.numbers,{type:"base64"});l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&Zp(tm(t.content)).forEach((function(e){o.push(e.id),a[e.id]={deps:[],location:r,type:Kp(e.messages[0].meta[1][0].data)}}))})),o.sort((function(e,t){return e-t}));var c=o.filter((function(e){return e>1})).map((function(e){return[e,Xp(e)]}));l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&Zp(tm(t.content)).forEach((function(e){e.messages.forEach((function(t){c.forEach((function(t){e.messages.some((function(e){return 11006!=Kp(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}(e.data,t[1])}))&&a[t[0]].deps.push(e.id)}))}))}))}));for(var h,f=_l.find(l,a[1].location),u=Zp(tm(f.content)),d=0;d<u.length;++d){var p=u[d];1==p.id&&(h=p)}var m=sm(Jp(h.messages[0].data)[1][0].data);for(u=Zp(tm((f=_l.find(l,a[m].location)).content)),d=0;d<u.length;++d)(p=u[d]).id==m&&(h=p);for(m=sm(Jp(h.messages[0].data)[2][0].data),u=Zp(tm((f=_l.find(l,a[m].location)).content)),d=0;d<u.length;++d)(p=u[d]).id==m&&(h=p);for(m=sm(Jp(h.messages[0].data)[2][0].data),u=Zp(tm((f=_l.find(l,a[m].location)).content)),d=0;d<u.length;++d)(p=u[d]).id==m&&(h=p);var g=Jp(h.messages[0].data);g[6][0].data=Xp(n.e.r+1),g[7][0].data=Xp(n.e.c+1);for(var v=sm(g[46][0].data),T=_l.find(l,a[v].location),y=Zp(tm(T.content)),w=0;w<y.length&&y[w].id!=v;++w);if(y[w].id!=v)throw"Bad ColumnRowUIDMapArchive";var E=Jp(y[w].messages[0].data);E[1]=[],E[2]=[],E[3]=[];for(var b=0;b<=n.e.c;++b){var S=[];S[1]=S[2]=[{type:0,data:Xp(b+420690)}],E[1].push({type:2,data:qp(S)}),E[2].push({type:0,data:Xp(b)}),E[3].push({type:0,data:Xp(b)})}E[4]=[],E[5]=[],E[6]=[];for(var A=0;A<=n.e.r;++A)(S=[])[1]=S[2]=[{type:0,data:Xp(A+726270)}],E[4].push({type:2,data:qp(S)}),E[5].push({type:0,data:Xp(A)}),E[6].push({type:0,data:Xp(A)});y[w].messages[0].data=qp(E),T.content=rm(Qp(y)),T.size=T.content.length,delete g[46];var x=Jp(g[4][0].data);x[7][0].data=Xp(n.e.r+1);var C=sm(Jp(x[1][0].data)[2][0].data);if((y=Zp(tm((T=_l.find(l,a[C].location)).content)))[0].id!=C)throw"Bad HeaderStorageBucket";var O=Jp(y[0].messages[0].data);for(A=0;A<i.length;++A){var R=Jp(O[2][0].data);R[1][0].data=Xp(A),R[4][0].data=Xp(i[A].length),O[2][A]={type:O[2][0].type,data:qp(R)}}y[0].messages[0].data=qp(O),T.content=rm(Qp(y)),T.size=T.content.length;var _=sm(x[2][0].data);if((y=Zp(tm((T=_l.find(l,a[_].location)).content)))[0].id!=_)throw"Bad HeaderStorageBucket";for(O=Jp(y[0].messages[0].data),b=0;b<=n.e.c;++b)(R=Jp(O[2][0].data))[1][0].data=Xp(b),R[4][0].data=Xp(n.e.r+1),O[2][b]={type:O[2][0].type,data:qp(R)};y[0].messages[0].data=qp(O),T.content=rm(Qp(y)),T.size=T.content.length;var P=sm(x[4][0].data);!function(){for(var e,t=_l.find(l,a[P].location),r=Zp(tm(t.content)),n=0;n<r.length;++n){var i=r[n];i.id==P&&(e=i)}var o=Jp(e.messages[0].data);o[3]=[];var c=[];s.forEach((function(e,t){c[1]=[{type:0,data:Xp(t)}],c[2]=[{type:0,data:Xp(1)}],c[3]=[{type:2,data:$p(e)}],o[3].push({type:2,data:qp(c)})})),e.messages[0].data=qp(o);var h=rm(Qp(r));t.content=h,t.size=t.content.length}();var k=Jp(x[3][0].data),D=k[1][0];delete k[2];var I=Jp(D.data),N=sm(I[2][0].data);!function(){for(var e,t=_l.find(l,a[N].location),r=Zp(tm(t.content)),o=0;o<r.length;++o){var c=r[o];c.id==N&&(e=c)}var h=Jp(e.messages[0].data);delete h[6],delete k[7];var f=new Uint8Array(h[5][0].data);h[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=Jp(f);u+=am(p,i[d],s),p[1][0].data=Xp(d),h[5].push({data:qp(p),type:2})}h[1]=[{type:0,data:Xp(n.e.c+1)}],h[2]=[{type:0,data:Xp(n.e.r+1)}],h[3]=[{type:0,data:Xp(u)}],h[4]=[{type:0,data:Xp(n.e.r+1)}],e.messages[0].data=qp(h);var m=rm(Qp(r));t.content=m,t.size=t.content.length}(),D.data=qp(I),x[3][0].data=qp(k),g[4][0].data=qp(x),h.messages[0].data=qp(g);var M=rm(Qp(u));return f.content=M,f.size=f.content.length,l}(e,t):"xlsb"==t.bookType?function(e,t){zu=1024,e&&!e.SSF&&(e.SSF=Yl($o));e&&e.SSF&&(Cl(),xl(e.SSF),t.revssf=Ml(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ld?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=Zu.indexOf(t.bookType)>-1,i={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};om(t=t||{});var s=Ql(),a="",o=0;t.cellXfs=[],Vd(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(Zl(s,a="docProps/core.xml",Df(e.Props,t)),i.coreprops.push(a),Of(t.rels,2,a,Af.CORE_PROPS),a="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&l.push(e.SheetNames[c]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Zl(s,a,Mf(e.Props)),i.extprops.push(a),Of(t.rels,3,a,Af.EXT_PROPS),e.Custprops!==e.Props&&Dl(e.Custprops||{}).length>0&&(Zl(s,a="docProps/custom.xml",Lf(e.Custprops)),i.custprops.push(a),Of(t.rels,4,a,Af.CUST_PROPS));for(o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},f=e.Sheets[e.SheetNames[o-1]];(f||{})["!type"];if(Zl(s,a="xl/worksheets/sheet"+o+"."+r,dp(o-1,a,t,e,h)),i.sheets.push(a),Of(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Af.WS[0]),f){var u=f["!comments"],d=!1,p="";u&&u.length>0&&(Zl(s,p="xl/comments"+o+"."+r,pp(u,p)),i.comments.push(p),Of(h,-1,"../comments"+o+"."+r,Af.CMNT),d=!0),f["!legacy"]&&d&&Zl(s,"xl/drawings/vmlDrawing"+o+".vml",Yu(o,f["!comments"])),delete f["!comments"],delete f["!legacy"]}h["!id"].rId1&&Zl(s,xf(a),Cf(h))}null!=t.Strings&&t.Strings.length>0&&(Zl(s,a="xl/sharedStrings."+r,function(e,t,r){return(".bin"===t.slice(-4)?vu:mu)(e,r)}(t.Strings,a,t)),i.strs.push(a),Of(t.wbrels,-1,"sharedStrings."+r,Af.SST));Zl(s,a="xl/workbook."+r,function(e,t){return(".bin"===t.slice(-4)?up:hp)(e)}(e,a)),i.workbooks.push(a),Of(t.rels,1,a,Af.WB),Zl(s,a="xl/theme/theme1.xml",Hu(e.Themes,t)),i.themes.push(a),Of(t.wbrels,-1,"theme/theme1.xml",Af.THEME),Zl(s,a="xl/styles."+r,function(e,t,r){return(".bin"===t.slice(-4)?ju:_u)(e,r)}(e,a,t)),i.styles.push(a),Of(t.wbrels,-1,"styles."+r,Af.STY),e.vbaraw&&n&&(Zl(s,a="xl/vbaProject.bin",e.vbaraw),i.vba.push(a),Of(t.wbrels,-1,"vbaProject.bin",Af.VBA));return Zl(s,a="xl/metadata."+r,function(e){return(".bin"===e.slice(-4)?Gu:$u)()}(a)),i.metadata.push(a),Of(t.wbrels,-1,"metadata."+r,Af.XLMETA),Zl(s,"[Content_Types].xml",Sf(i,t)),Zl(s,"_rels/.rels",Cf(t.rels)),Zl(s,"xl/_rels/workbook."+r+".rels",Cf(t.wbrels)),delete t.revssf,delete t.ssf,s}(e,t):function(e,t){zu=1024,e&&!e.SSF&&(e.SSF=Yl($o));e&&e.SSF&&(Cl(),xl(e.SSF),t.revssf=Ml(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ld?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Zu.indexOf(t.bookType)>-1,i={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};om(t=t||{});var s=Ql(),a="",o=0;t.cellXfs=[],Vd(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(Zl(s,a="docProps/core.xml",Df(e.Props,t)),i.coreprops.push(a),Of(t.rels,2,a,Af.CORE_PROPS),a="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&l.push(e.SheetNames[c]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Zl(s,a,Mf(e.Props)),i.extprops.push(a),Of(t.rels,3,a,Af.EXT_PROPS),e.Custprops!==e.Props&&Dl(e.Custprops||{}).length>0&&(Zl(s,a="docProps/custom.xml",Lf(e.Custprops)),i.custprops.push(a),Of(t.rels,4,a,Af.CUST_PROPS));var h=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];(u||{})["!type"];if(Zl(s,a="xl/worksheets/sheet"+o+"."+r,$d(o-1,t,e,f)),i.sheets.push(a),Of(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Af.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach((function(e){e[1].forEach((function(e){1==e.T&&(g=!0)}))})),g&&(Zl(s,m="xl/threadedComments/threadedComment"+o+"."+r,Ku(d,h,t)),i.threadedcomments.push(m),Of(f,-1,"../threadedComments/threadedComment"+o+"."+r,Af.TCMNT)),Zl(s,m="xl/comments"+o+"."+r,Xu(d)),i.comments.push(m),Of(f,-1,"../comments"+o+"."+r,Af.CMNT),p=!0}u["!legacy"]&&p&&Zl(s,"xl/drawings/vmlDrawing"+o+".vml",Yu(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}f["!id"].rId1&&Zl(s,xf(a),Cf(f))}null!=t.Strings&&t.Strings.length>0&&(Zl(s,a="xl/sharedStrings."+r,mu(t.Strings,t)),i.strs.push(a),Of(t.wbrels,-1,"sharedStrings."+r,Af.SST));Zl(s,a="xl/workbook."+r,hp(e)),i.workbooks.push(a),Of(t.rels,1,a,Af.WB),Zl(s,a="xl/theme/theme1.xml",Hu(e.Themes,t)),i.themes.push(a),Of(t.wbrels,-1,"theme/theme1.xml",Af.THEME),Zl(s,a="xl/styles."+r,_u(e,t)),i.styles.push(a),Of(t.wbrels,-1,"styles."+r,Af.STY),e.vbaraw&&n&&(Zl(s,a="xl/vbaProject.bin",e.vbaraw),i.vba.push(a),Of(t.wbrels,-1,"vbaProject.bin",Af.VBA));Zl(s,a="xl/metadata."+r,$u()),i.metadata.push(a),Of(t.wbrels,-1,"metadata."+r,Af.XLMETA),h.length>1&&(Zl(s,a="xl/persons/person.xml",function(e){var t=[ec,vc("personList",null,{xmlns:Ac,"xmlns:x":Dc[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(vc("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}(h)),i.people.push(a),Of(t.wbrels,-1,"persons/person.xml",Af.PEOPLE));return Zl(s,"[Content_Types].xml",Sf(i,t)),Zl(s,"_rels/.rels",Cf(t.rels)),Zl(s,"xl/_rels/workbook."+r+".rels",Cf(t.wbrels)),delete t.revssf,delete t.ssf,s}(e,t)}function cm(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return kl(t.file,_l.write(e,{type:Co?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return _l.write(e,t)}function hm(e,t){var r=Yl(t||{});return function(e,t){var r={},n=Co?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";t.compression&&(r.compression="DEFLATE");if(t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var i=e.FullPaths?_l.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof i){if("binary"==t.type||"base64"==t.type)return i;i=new Uint8Array(ko(i))}return t.password&&"undefined"!=typeof encrypt_agile?cm(encrypt_agile(i,t.password),t):"file"===t.type?kl(t.file,i):"string"==t.type?fc(i):i}(lm(e,r),r)}function fm(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return Ao(uc(n));case"binary":return uc(n);case"string":return e;case"file":return kl(t.file,n,"utf8");case"buffer":return Co?Oo(n,"utf8"):"undefined"!=typeof TextEncoder?(new TextEncoder).encode(n):fm(n,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function um(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?Ao(r):"string"==t.type?fc(r):r;case"file":return kl(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function dm(e,t){yo(),cp(e);var r=Yl(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var n=dm(e,r);return r.type="array",ko(n)}var i=0;if(r.sheet&&(i="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[i]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return fm(wp(e,r),r);case"slk":case"sylk":return fm(cu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"htm":case"html":return fm(Fp(e.Sheets[e.SheetNames[i]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return Ao(e);case"binary":case"string":return e;case"file":return kl(t.file,e,"binary");case"buffer":return Co?Oo(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(wm(e.Sheets[e.SheetNames[i]],r),r);case"csv":return fm(ym(e.Sheets[e.SheetNames[i]],r),r,"\ufeff");case"dif":return fm(hu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"dbf":return um(lu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"prn":return fm(uu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"rtf":return fm(yu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"eth":return fm(fu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"fods":return fm(Hp(e,r),r);case"wk1":return um(du.sheet_to_wk1(e.Sheets[e.SheetNames[i]],r),r);case"wk3":return um(du.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),um(Mp(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return cm(Ap(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return hm(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function pm(e,t,r){var n={type:"file"};return n.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}(n),dm(e,n)}function mm(e,t,r,n,i,s,a,o){var l=_h(r),c=o.defval,h=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),f=!0,u=1===i?[]:{};if(1!==i)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!a||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=a?e[r][d]:e[n[d]+l];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==c)u[s[d]]=c;else{if(!h||null!==m)continue;u[s[d]]=null}else u[s[d]]=h&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:Uh(p,m,o);null!=m&&(f=!1)}}else{if(void 0===c)continue;null!=s[d]&&(u[s[d]]=c)}}return{row:u,isempty:f}}function gm(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,i=1,s=[],a=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},h=null!=c.range?c.range:e["!ref"];switch(1===c.header?n=1:"A"===c.header?n=2:Array.isArray(c.header)?n=3:null==c.header&&(n=0),typeof h){case"string":l=Lh(h);break;case"number":(l=Lh(e["!ref"])).s.r=h;break;default:l=h}n>0&&(i=0);var f=_h(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,y={};g&&!e[v]&&(e[v]=[]);var w=c.skipHidden&&e["!cols"]||[],E=c.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(w[T]||{}).hidden)switch(u[T]=kh(T),r=g?e[v][T]:e[u[T]+f],n){case 1:s[T]=T-l.s.c;break;case 2:s[T]=u[T];break;case 3:s[T]=c.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=a=Uh(r,null,c),m=y[a]||0){do{o=a+"_"+m++}while(y[o]);y[a]=m,y[o]=1}else y[a]=1;s[T]=o}for(v=l.s.r+i;v<=l.e.r;++v)if(!(E[v]||{}).hidden){var b=mm(e,l,v,u,n,s,g,c);(!1===b.isempty||(1===n?!1!==c.blankrows:c.blankrows))&&(d[p++]=b.row)}return d.length=p,d}var vm=/"/g;function Tm(e,t,r,n,i,s,a,o){for(var l=!0,c=[],h="",f=_h(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+f];if(null==d)h="";else if(null!=d.v){l=!1,h=""+(o.rawNumbers&&"n"==d.t?d.v:Uh(d,null,o));for(var p=0,m=0;p!==h.length;++p)if((m=h.charCodeAt(p))===i||m===s||34===m||o.forceQuotes){h='"'+h.replace(vm,'""')+'"';break}"ID"==h&&(h='"ID"')}else null==d.f||d.F?h="":(l=!1,(h="="+d.f).indexOf(",")>=0&&(h='"'+h.replace(vm,'""')+'"'));c.push(h)}return!1===o.blankrows&&l?null:c.join(a)}function ym(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var i=Lh(e["!ref"]),s=void 0!==n.FS?n.FS:",",a=s.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),c=new RegExp(("|"==s?"\\|":s)+"+$"),h="",f=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=i.s.c;p<=i.e.c;++p)(u[p]||{}).hidden||(f[p]=kh(p));for(var m=0,g=i.s.r;g<=i.e.r;++g)(d[g]||{}).hidden||null!=(h=Tm(e,i,g,f,a,l,s,n))&&(n.strip&&(h=h.replace(c,"")),(h||!1!==n.blankrows)&&r.push((m++?o:"")+h));return delete n.dense,r.join("")}function wm(e,t){return t||(t={}),t.FS="\t",t.RS="\n",ym(e,t)}function Em(e,t,r){var n,i=r||{},s=+!i.skipHeader,a=e||{},o=0,l=0;if(a&&null!=i.origin)if("number"==typeof i.origin)o=i.origin;else{var c="string"==typeof i.origin?Dh(i.origin):i.origin;o=c.r,l=c.c}var h={s:{c:0,r:0},e:{c:l,r:o+t.length-1+s}};if(a["!ref"]){var f=Lh(a["!ref"]);h.e.c=Math.max(h.e.c,f.e.c),h.e.r=Math.max(h.e.r,f.e.r),-1==o&&(o=f.e.r+1,h.e.r=o+t.length-1+s)}else-1==o&&(o=0,h.e.r=t.length-1+s);var u=i.header||[],d=0;t.forEach((function(e,t){Dl(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var c=e[r],h="z",f="",p=Ih({c:l+d,r:o+t+s});n=bm(a,p),!c||"object"!=typeof c||c instanceof Date?("number"==typeof c?h="n":"boolean"==typeof c?h="b":"string"==typeof c?h="s":c instanceof Date?(h="d",i.cellDates||(h="n",c=Fl(c)),f=i.dateNF||$o[14]):null===c&&i.nullError&&(h="e",c=0),n?(n.t=h,n.v=c,delete n.w,delete n.R,f&&(n.z=f)):a[p]=n={t:h,v:c},f&&(n.z=f)):a[p]=c}))})),h.e.c=Math.max(h.e.c,l+u.length-1);var p=_h(o);if(s)for(d=0;d<u.length;++d)a[kh(d+l)+p]={t:"s",v:u[d]};return a["!ref"]=Mh(h),a}function bm(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=Dh(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return bm(e,Ih("number"!=typeof t?t:{r:t,c:r||0}))}function Sm(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var Am={encode_col:kh,encode_row:_h,encode_cell:Ih,encode_range:Mh,decode_col:Ph,decode_row:Rh,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:Dh,decode_range:Nh,format_cell:Uh,sheet_add_aoa:Vh,sheet_add_json:Em,sheet_add_dom:Up,aoa_to_sheet:Wh,json_to_sheet:function(e,t){return Em(null,e,t)},table_to_sheet:Bp,table_to_book:function(e,t){return Bh(Bp(e,t),t)},sheet_to_csv:ym,sheet_to_txt:wm,sheet_to_json:gm,sheet_to_html:Fp,sheet_to_formulae:function(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var i,s=Lh(e["!ref"]),a="",o=[],l=[],c=Array.isArray(e);for(i=s.s.c;i<=s.e.c;++i)o[i]=kh(i);for(var h=s.s.r;h<=s.e.r;++h)for(a=_h(h),i=s.s.c;i<=s.e.c;++i)if(r=o[i]+a,n="",void 0!==(t=c?(e[h]||[])[i]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}l[l.length]=r+"="+n}return l},sheet_to_row_object_array:gm,sheet_get_cell:bm,book_new:function(){return{SheetNames:[],Sheets:{}}},book_append_sheet:function(e,t,r,n){var i=1;if(!r)for(;i<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+i);++i,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);i=s&&+s[2]||0;var a=s&&s[1]||r;for(++i;i<=65535&&-1!=e.SheetNames.indexOf(r=a+i);++i);}if(lp(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r},book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:Sm,cell_set_internal_link:function(e,t,r){return Sm(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,n){for(var i="string"!=typeof t?t:Lh(t),s="string"==typeof t?t:Mh(t),a=i.s.r;a<=i.e.r;++a)for(var o=i.s.c;o<=i.e.c;++o){var l=bm(e,a,o);l.t="n",l.F=s,delete l.v,a==i.s.r&&o==i.s.c&&(l.f=r,n&&(l.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};export{hn as A,Am as a,s as j,co as m,uo as u,pm as w};
