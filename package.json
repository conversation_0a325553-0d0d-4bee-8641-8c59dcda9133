{"name": "sat-mobile", "private": true, "version": "1.0.0", "type": "module", "description": "SAT Mobile - A mobile-first church membership management application", "author": "SAT Mobile Team", "homepage": ".", "scripts": {"dev": "vite", "build": "vite build && node scripts/optimize-build.js", "build:fast": "vite build", "preview": "vite preview", "build:mobile": "vite build --mode production && node scripts/optimize-build.js", "serve": "vite preview --host 0.0.0.0 --port 3000", "generate-icons": "open generate-app-icons.html", "analyze": "vite build --mode analyze", "test:firebase": "node scripts/test-firebase.js", "setup:firebase": "node scripts/setup-firebase.js", "demo:firebase": "node scripts/demo-firebase.js", "migration:guide": "echo 'See FIREBASE_MIGRATION_GUIDE.md for detailed migration instructions'", "migrate:auth-links": "node scripts/migrate-auth-links.js migrate", "rollback:auth-links": "node scripts/migrate-auth-links.js rollback", "migration:dynamic-links": "echo 'See FIREBASE_DYNAMIC_LINKS_MIGRATION.md for Dynamic Links migration guide'"}, "dependencies": {"@capacitor/android": "^7.4.1", "@capacitor/cli": "^7.4.1", "@capacitor/core": "^7.4.1", "@capacitor/filesystem": "^7.1.2", "@google/generative-ai": "^0.24.1", "chart.js": "^4.5.0", "chartjs-to-image": "^1.2.2", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "firebase": "^11.9.1", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "recharts": "^3.0.2", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "firebase-admin": "^13.0.1", "tailwindcss": "^4.1.11", "terser": "^5.42.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}