/* Enhanced Church Connect Mobile Styles */
/* Tailwind CSS temporarily disabled to focus on Firebase testing */

/* Global Animations */
@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes zoomIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotateIn {
  0% {
    transform: rotate(-180deg) scale(0.5);
    opacity: 0;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(103, 126, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(103, 126, 234, 0.8), 0 0 30px rgba(103, 126, 234, 0.6);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Utility Classes */
.slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.slide-in-bottom {
  animation: slideInFromBottom 0.6s ease-out;
}

.slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

.rotate-in {
  animation: rotateIn 0.8s ease-out;
}

.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite;
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typewriter 2s steps(40, end), blink 0.75s step-end infinite;
}

/* Enhanced Glass Morphism */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Interactive Elements - Optimized for performance */
.interactive-card {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s ease;
  cursor: pointer;
  will-change: transform;
}

.interactive-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.interactive-card:active {
  transform: translateY(-2px) scale(1.005);
}

/* Button Enhancements - Performance optimized */
.btn-primary-enhanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.btn-primary-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-primary-enhanced:hover::before {
  left: 100%;
}

.btn-primary-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(103, 126, 234, 0.4);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile Touch Enhancements */
.touch-pan-y {
  touch-action: pan-y;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Modal scroll fixes for mobile */
.modal-backdrop {
  touch-action: none;
  overscroll-behavior: contain;
}

.modal-content {
  touch-action: auto;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

.modal-scrollable {
  touch-action: pan-y;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* Gesture feedback */
.gesture-feedback {
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

/* Responsive Enhancements - Performance optimized */
@media (max-width: 768px) {
  .glass {
    backdrop-filter: blur(6px);
  }

  .interactive-card:hover {
    transform: translateY(-2px) scale(1.005);
  }

  /* Enhanced touch targets for mobile */
  button, .interactive-card {
    min-height: 44px;
    min-width: 44px;
  }

  /* Reduce container padding on mobile */
  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Disable expensive animations on mobile */
  .floating, .shimmer {
    animation: none;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .glass {
    backdrop-filter: blur(6px);
  }

  /* Further reduce spacing on very small screens */
  .container {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  /* Ensure text doesn't get too small */
  .text-xs {
    font-size: 0.75rem;
  }

  /* Compact buttons on small screens */
  button {
    min-height: 40px;
    min-width: 40px;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Custom Scrollbar for specific containers */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 10px;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #5a67d8, #6b46c1);
}

/* Confirmation Modal Animations */
.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.4s ease-out;
}

@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating Action Button Animation */
.floating {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

/* Toast Animations */
@keyframes shrink {
  from { width: 100%; }
  to { width: 0%; }
}

/* Gentle bounce animation for interactive elements */
@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

/* Enhanced Sidebar Scrollbar */
.sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(243, 244, 246, 0.3);
  scroll-behavior: smooth;
}

.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* Scroll fade indicators */
.scroll-fade-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  height: 20px;
  position: sticky;
  top: 0;
  z-index: 10;
  pointer-events: none;
}

.scroll-fade-bottom {
  background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  height: 20px;
  position: sticky;
  bottom: 0;
  z-index: 10;
  pointer-events: none;
}

/* Month Navigation Enhancements */
.month-nav-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.month-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.month-nav-button:hover::before {
  left: 100%;
}

.month-nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.month-nav-button:active {
  transform: scale(0.98);
}

/* Month display styling */
.month-display {
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 1px solid rgba(103, 126, 234, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px 16px;
  font-weight: 600;
  color: #374151;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* Chart Animations */
@keyframes drawLine {
  from {
    stroke-dashoffset: 200;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Interactive Chart Enhancements */
.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-bar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom;
}

.chart-bar:hover {
  filter: brightness(1.1);
  transform: scaleY(1.02);
}

.chart-point {
  transition: all 0.2s ease-out;
  cursor: pointer;
}

.chart-point:hover {
  transform: scale(1.5);
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4));
}

.chart-tooltip {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
