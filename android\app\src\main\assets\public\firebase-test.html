<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Database Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid #f44336; }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid #2196F3; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        h1, h2 { text-align: center; margin-bottom: 30px; }
        .emoji { font-size: 2em; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🔥</span>Firebase Database Test (No Auth)</h1>
        
        <div id="status" class="status info">
            Testing Firebase Firestore without authentication...
        </div>
        
        <div>
            <button onclick="testConnection()">Test Connection</button>
            <button onclick="testWrite()">Test Write</button>
            <button onclick="testRead()">Test Read</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, doc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDkyjDhyz_LCbUpRgftD2qo31e5SteAiKg",
            authDomain: "sat-mobile-de6f1.firebaseapp.com",
            projectId: "sat-mobile-de6f1",
            storageBucket: "sat-mobile-de6f1.firebasestorage.app",
            messagingSenderId: "1076014285349",
            appId: "1:1076014285349:web:d72d460aefe5ca8d76b5cc"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        // Test Firebase connection
        window.testConnection = async function() {
            try {
                addResult('🔄 Testing Firebase connection...', 'info');
                
                // Try to access Firestore
                const testRef = collection(db, 'test');
                addResult('✅ Firebase Firestore connected successfully!', 'success');
                showStatus('✅ Firebase is ready for database operations', 'success');
                
            } catch (error) {
                addResult(`❌ Connection failed: ${error.message}`, 'error');
                showStatus('❌ Firebase connection failed', 'error');
            }
        };

        // Test writing data
        window.testWrite = async function() {
            try {
                addResult('🔄 Testing write operation...', 'info');
                
                const testData = {
                    message: 'Hello from Firebase test!',
                    timestamp: new Date(),
                    type: 'test_document'
                };
                
                const docRef = await addDoc(collection(db, 'test'), testData);
                addResult(`✅ Write successful! Document ID: ${docRef.id}`, 'success');
                
            } catch (error) {
                addResult(`❌ Write failed: ${error.message}`, 'error');
                
                if (error.code === 'permission-denied') {
                    addResult('💡 Solution: Update Firestore security rules to allow read/write without authentication', 'info');
                }
            }
        };

        // Test reading data
        window.testRead = async function() {
            try {
                addResult('🔄 Testing read operation...', 'info');
                
                const querySnapshot = await getDocs(collection(db, 'test'));
                addResult(`✅ Read successful! Found ${querySnapshot.size} documents`, 'success');
                
                querySnapshot.forEach((doc) => {
                    const data = doc.data();
                    addResult(`📄 Document ${doc.id}: ${data.message || 'No message'}`, 'info');
                });
                
            } catch (error) {
                addResult(`❌ Read failed: ${error.message}`, 'error');
                
                if (error.code === 'permission-denied') {
                    addResult('💡 Solution: Update Firestore security rules to allow read/write without authentication', 'info');
                }
            }
        };

        // Clear results
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            showStatus('Results cleared', 'info');
        };

        // Auto-test connection on load
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🔥 Firebase initialized', 'success');
            testConnection();
        });
    </script>
</body>
</html>
