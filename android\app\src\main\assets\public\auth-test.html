<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid #f44336; }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid #2196F3; }
        input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }
        input::placeholder { color: rgba(255, 255, 255, 0.7); }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        h1 { text-align: center; margin-bottom: 30px; }
        .emoji { font-size: 2em; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🔐</span>Firebase Auth Test</h1>
        
        <div id="status" class="status info">
            Ready to test Firebase Authentication
        </div>
        
        <div>
            <input type="email" id="email" placeholder="Enter email (e.g., <EMAIL>)" value="<EMAIL>">
            <input type="password" id="password" placeholder="Enter password">
            <button onclick="testLogin()">Test Login</button>
            <button onclick="checkAuthStatus()">Check Auth Status</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDkyjDhyz_LCbUpRgftD2qo31e5SteAiKg",
            authDomain: "sat-mobile-de6f1.firebaseapp.com",
            projectId: "sat-mobile-de6f1",
            storageBucket: "sat-mobile-de6f1.firebasestorage.app",
            messagingSenderId: "1076014285349",
            appId: "1:1076014285349:web:d72d460aefe5ca8d76b5cc"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        // Test login function
        window.testLogin = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                addResult('❌ Please enter both email and password', 'error');
                return;
            }
            
            try {
                addResult('🔄 Attempting to sign in...', 'info');
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                addResult(`✅ Login successful! User: ${user.email}`, 'success');
                addResult(`📧 User UID: ${user.uid}`, 'info');
                showStatus('✅ Authentication working correctly!', 'success');
                
            } catch (error) {
                addResult(`❌ Login failed: ${error.code} - ${error.message}`, 'error');
                
                // Provide specific guidance based on error
                if (error.code === 'auth/user-not-found') {
                    addResult('💡 Solution: Create this user in Firebase Console → Authentication → Users', 'info');
                } else if (error.code === 'auth/wrong-password') {
                    addResult('💡 Solution: Check the password or reset it in Firebase Console', 'info');
                } else if (error.code === 'auth/invalid-email') {
                    addResult('💡 Solution: Check the email format', 'info');
                } else if (error.code === 'auth/operation-not-allowed') {
                    addResult('💡 Solution: Enable Email/Password authentication in Firebase Console', 'info');
                }
            }
        };

        // Check auth status
        window.checkAuthStatus = function() {
            addResult('🔄 Checking authentication status...', 'info');
            
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    addResult(`✅ User is signed in: ${user.email}`, 'success');
                    showStatus('✅ User authenticated', 'success');
                } else {
                    addResult('ℹ️ No user is currently signed in', 'info');
                    showStatus('🔐 Please sign in', 'info');
                }
            });
        };

        // Auto-check status on load
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🔥 Firebase initialized successfully', 'success');
            checkAuthStatus();
        });
    </script>
</body>
</html>
