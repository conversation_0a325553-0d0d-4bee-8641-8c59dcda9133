import{j as e}from"./utils-DJ-5fhzp.js";import{r as t,R as s}from"./charts-DCWOOedL.js";import{u as a,G as n,I as i,E as c,T as l,e as r,h as d,C as o,B as m}from"./index-DXKcng7s.js";import{T as x,B as h}from"./Badge-BonlZcKp.js";import"./vendor-CMmhtoO5.js";import"./icons-CQztrb4S.js";const g=()=>{const{bacentas:g,members:u,attendanceRecords:p,openBacentaForm:j,deleteBacentaHandler:f,showConfirmation:b,isLoading:N,switchTab:v,displayedSundays:w}=a(),[y,D]=t.useState(""),[k,B]=t.useState({isOpen:!1,bacenta:null,position:{x:0,y:0}}),C=t.useMemo((()=>g.filter((e=>{if(y){const t=y.toLowerCase();return e.name.toLowerCase().includes(t)}return!0})).sort(((e,t)=>e.name.localeCompare(t.name)))),[g,y]),M=()=>{B({isOpen:!1,bacenta:null,position:{x:0,y:0}})};s.useEffect((()=>{const e=()=>{k.isOpen&&M()};return k.isOpen&&(document.addEventListener("click",e),document.addEventListener("touchstart",e)),()=>{document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}}),[k.isOpen]);const A=e=>u.filter((t=>t.bacentaId===e)).length,L=t.useMemo((()=>{const e=new Date,t=e.getFullYear(),s=e.getMonth(),a=[],n=new Date(t,s,1);for(;0!==n.getDay();)n.setDate(n.getDate()+1);for(;n.getMonth()===s;)a.push(n.toISOString().split("T")[0]),n.setDate(n.getDate()+7);return a}),[]),S=e=>{const t=u.filter((t=>t.bacentaId===e));if(0===t.length||0===L.length)return 0;const s=t.length*L.length,a=p.filter((e=>t.some((t=>t.id===e.memberId))&&L.includes(e.date)&&"Present"===e.status)).length;return s>0?Math.round(a/s*100):0},E=[{key:"name",header:"Bacenta Name",width:"30%",render:t=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center",children:e.jsx(n,{className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold text-gray-900 text-lg",children:t.name}),e.jsxs("div",{className:"text-sm text-gray-500",children:["ID: ",t.id.slice(0,8),"..."]})]})]})},{key:"memberCount",header:"Members",width:"15%",align:"center",render:t=>{const s=A(t.id);return e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(r,{className:"w-4 h-4 text-blue-500"}),e.jsx("span",{className:"font-semibold text-lg",children:s})]}),e.jsx(h,{variant:s>10?"success":s>5?"warning":"secondary",size:"sm",className:"mt-1",children:s>10?"Large":s>5?"Medium":"Small"})]})}},{key:"attendanceRate",header:"Attendance Rate",width:"15%",align:"center",render:t=>{const s=S(t.id);return e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"w-4 h-4 text-green-500"}),e.jsxs("span",{className:"font-semibold text-lg",children:[s,"%"]})]}),e.jsx(h,{variant:s>=80?"success":s>=60?"warning":"danger",size:"sm",className:"mt-1",children:s>=80?"Excellent":s>=60?"Good":"Needs Attention"})]})}},{key:"recentActivity",header:"Recent Activity",width:"15%",align:"center",render:t=>{const s=(e=>{const t=new Date;return t.setDate(t.getDate()-30),u.filter((s=>s.bacentaId===e&&new Date(s.createdDate)>=t)).length})(t.id);return e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"w-4 h-4 text-orange-500"}),e.jsx("span",{className:"font-semibold text-lg",children:s})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:"New members (30d)"})]})}},{key:"status",header:"Status",width:"15%",align:"center",render:t=>{const s=A(t.id),a=S(t.id);let n="Active",i="success";return 0===s?(n="Empty",i="danger"):a<50&&(n="Low Activity",i="warning"),e.jsx(h,{variant:i,size:"sm",children:n})}},{key:"actions",header:"Actions",width:"10%",align:"center",render:t=>e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(m,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),j(t)},className:"p-2 hover:bg-blue-100",title:"Edit Bacenta",children:e.jsx(c,{className:"w-4 h-4 text-blue-600"})}),e.jsx(m,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),f(t.id)},className:"p-2 hover:bg-red-100",title:"Delete Bacenta",children:e.jsx(l,{className:"w-4 h-4 text-red-600"})})]})}],O=(new Date).toLocaleDateString("en-US",{month:"long",year:"numeric"});return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[e.jsx(n,{className:"w-5 h-5 mr-2 text-purple-600"}),"Bacentas Overview - ",O]}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Attendance data for ",L.length," Sunday",1!==L.length?"s":""," this month"]})]}),e.jsx("div",{className:"w-full sm:w-64",children:e.jsx(i,{placeholder:"Search bacentas...",value:y,onChange:e=>D(e.target.value),className:"border-0 bg-white/50 focus:bg-white/80 transition-colors"})})]})}),e.jsx(x,{data:C,columns:E,loading:N,emptyMessage:y?"No bacentas match your search":"No bacentas created yet",onRowClick:e=>v({id:e.id,name:e.name}),onRowLongPress:(e,t)=>{const s="clientX"in t?t.clientX:t.touches[0].clientX,a="clientY"in t?t.clientY:t.touches[0].clientY;B({isOpen:!0,bacenta:e,position:{x:Math.min(s,window.innerWidth-200),y:Math.min(a,window.innerHeight-120)}})}}),C.length>0&&e.jsx("div",{className:"glass p-4 rounded-2xl shadow-lg",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Total Bacentas"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Total Members"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:C.reduce(((e,t)=>e+A(t.id)),0)})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Avg Attendance"}),e.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[C.length>0?Math.round(C.reduce(((e,t)=>e+S(t.id)),0)/C.length):0,"%"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold text-gray-900",children:"Active Bacentas"}),e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:C.filter((e=>A(e.id)>0)).length})]})]})}),k.isOpen&&k.bacenta&&e.jsxs("div",{className:"fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200 py-2 min-w-[160px]",style:{left:k.position.x,top:k.position.y},onClick:e=>e.stopPropagation(),children:[e.jsxs("button",{onClick:()=>{k.bacenta&&(j(k.bacenta),M())},className:"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-3",children:[e.jsx(c,{className:"w-4 h-4 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Edit Bacenta"})]}),e.jsxs("button",{onClick:()=>{if(k.bacenta){const e=u.filter((e=>e.bacentaId===k.bacenta.id)).length;b("deleteBacenta",{bacenta:k.bacenta,memberCount:e},(()=>{f(k.bacenta.id)})),M()}},className:"w-full px-4 py-3 text-left hover:bg-red-50 transition-colors duration-200 flex items-center space-x-3",children:[e.jsx(l,{className:"w-4 h-4 text-red-600"}),e.jsx("span",{className:"text-sm font-medium text-red-700",children:"Delete Bacenta"})]})]})]})};export{g as default};
