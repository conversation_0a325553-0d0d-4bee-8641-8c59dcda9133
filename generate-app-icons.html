<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate App Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            background: #334155;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #475569;
        }
        .instructions {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SAT Mobile - App Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" to create app icons</li>
                <li>Right-click each icon and "Save image as..."</li>
                <li>Save them in the public/ folder with the exact names shown</li>
                <li>Use these icons for your Median.co deployment</li>
            </ol>
        </div>

        <button onclick="generateIcons()">Generate Icons</button>
        <button onclick="downloadAll()">Download All Icons</button>

        <div class="icon-preview" id="iconPreview"></div>
    </div>

    <script>
        function createIcon(size, filename) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#334155');
            gradient.addColorStop(1, '#1e293b');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Church icon (simplified)
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('⛪', size / 2, size / 2);

            // Add subtle border
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, size - 2, size - 2);

            return canvas;
        }

        function generateIcons() {
            const sizes = [
                { size: 192, name: 'icon-192.png' },
                { size: 512, name: 'icon-512.png' },
                { size: 144, name: 'icon-144.png' },
                { size: 96, name: 'icon-96.png' },
                { size: 72, name: 'icon-72.png' },
                { size: 48, name: 'icon-48.png' }
            ];

            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';

            sizes.forEach(({ size, name }) => {
                const canvas = createIcon(size, name);
                
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const title = document.createElement('h4');
                title.textContent = `${size}x${size}`;
                
                const filename = document.createElement('p');
                filename.textContent = name;
                filename.style.fontSize = '12px';
                filename.style.color = '#666';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadCanvas(canvas, name);
                
                item.appendChild(title);
                item.appendChild(canvas);
                item.appendChild(filename);
                item.appendChild(downloadBtn);
                
                preview.appendChild(item);
            });
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAll() {
            const canvases = document.querySelectorAll('canvas');
            const filenames = ['icon-192.png', 'icon-512.png', 'icon-144.png', 'icon-96.png', 'icon-72.png', 'icon-48.png'];
            
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadCanvas(canvas, filenames[index]);
                }, index * 500);
            });
        }

        // Generate icons on page load
        window.onload = generateIcons;
    </script>
</body>
</html>
